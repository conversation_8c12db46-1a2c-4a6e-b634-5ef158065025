/**
 * Performance Optimization Module
 * Provides performance monitoring and optimization utilities
 */

class PerformanceManager {
    constructor() {
        this.isInitialized = false;
        this.metrics = {
            loadTime: 0,
            renderTime: 0,
            interactionTime: 0,
            memoryUsage: 0
        };
        this.observers = new Map();
        this.performanceEntries = [];
        this.maxEntries = 100;
    }
    
    /**
     * Initialize performance manager
     */
    init() {
        if (this.isInitialized) return;
        
        // Set up performance monitoring
        this.setupPerformanceMonitoring();
        
        // Set up intersection observer for lazy loading
        this.setupLazyLoading();
        
        // Set up resource optimization
        this.setupResourceOptimization();

        // Monitor Core Web Vitals
        this.monitorCoreWebVitals();

        this.isInitialized = true;
        console.log('Performance manager initialized');
    }
    
    /**
     * Set up performance monitoring
     */
    setupPerformanceMonitoring() {
        // Monitor page load performance
        window.addEventListener('load', () => {
            this.measureLoadPerformance();
        });
        
        // Monitor navigation performance
        if ('PerformanceObserver' in window) {
            this.setupPerformanceObserver();
        }
        
        // Monitor memory usage
        this.monitorMemoryUsage();
    }
    
    /**
     * Set up performance observer
     */
    setupPerformanceObserver() {
        try {
            // Observe navigation timing
            const navObserver = new PerformanceObserver((list) => {
                this.handleNavigationEntries(list.getEntries());
            });
            navObserver.observe({ entryTypes: ['navigation'] });
            this.observers.set('navigation', navObserver);
            
            // Observe resource timing
            const resourceObserver = new PerformanceObserver((list) => {
                this.handleResourceEntries(list.getEntries());
            });
            resourceObserver.observe({ entryTypes: ['resource'] });
            this.observers.set('resource', resourceObserver);
            
            // Observe paint timing
            const paintObserver = new PerformanceObserver((list) => {
                this.handlePaintEntries(list.getEntries());
            });
            paintObserver.observe({ entryTypes: ['paint'] });
            this.observers.set('paint', paintObserver);
            
        } catch (error) {
            console.warn('Performance observer setup failed:', error);
        }
    }
    
    /**
     * Handle navigation timing entries
     * @param {Array} entries - Performance entries
     */
    handleNavigationEntries(entries) {
        entries.forEach(entry => {
            this.metrics.loadTime = entry.loadEventEnd - entry.loadEventStart;
            this.metrics.renderTime = entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart;
            
            this.addPerformanceEntry({
                type: 'navigation',
                name: entry.name,
                duration: entry.duration,
                loadTime: this.metrics.loadTime,
                renderTime: this.metrics.renderTime
            });
        });
    }
    
    /**
     * Handle resource timing entries
     * @param {Array} entries - Performance entries
     */
    handleResourceEntries(entries) {
        entries.forEach(entry => {
            // Track slow resources
            if (entry.duration > 1000) { // Resources taking more than 1 second
                this.addPerformanceEntry({
                    type: 'slow-resource',
                    name: entry.name,
                    duration: entry.duration,
                    size: entry.transferSize || 0
                });
            }
        });
    }
    
    /**
     * Handle paint timing entries
     * @param {Array} entries - Performance entries
     */
    handlePaintEntries(entries) {
        entries.forEach(entry => {
            this.addPerformanceEntry({
                type: 'paint',
                name: entry.name,
                startTime: entry.startTime
            });
        });
    }
    
    /**
     * Measure page load performance
     */
    measureLoadPerformance() {
        if (!performance.timing) return;
        
        const timing = performance.timing;
        const loadTime = timing.loadEventEnd - timing.navigationStart;
        const domReady = timing.domContentLoadedEventEnd - timing.navigationStart;
        const firstPaint = timing.responseStart - timing.navigationStart;
        
        this.metrics.loadTime = loadTime;
        
        console.log('Performance metrics:', {
            loadTime: `${loadTime}ms`,
            domReady: `${domReady}ms`,
            firstPaint: `${firstPaint}ms`
        });
        
        // Dispatch performance event
        this.dispatchPerformanceEvent('performance:load-measured', {
            loadTime,
            domReady,
            firstPaint
        });
    }
    
    /**
     * Set up lazy loading for images and content
     */
    setupLazyLoading() {
        if ('IntersectionObserver' in window) {
            const lazyImageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.loadLazyImage(entry.target);
                        lazyImageObserver.unobserve(entry.target);
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.01
            });
            
            this.observers.set('lazyImages', lazyImageObserver);
            
            // Observe existing lazy images
            this.observeLazyImages();
            
            // Observe new lazy images added dynamically
            this.setupMutationObserver();
        }
    }
    
    /**
     * Observe lazy images
     */
    observeLazyImages() {
        const lazyImages = document.querySelectorAll('img[data-src], [data-lazy]');
        const observer = this.observers.get('lazyImages');
        
        if (observer) {
            lazyImages.forEach(img => observer.observe(img));
        }
    }
    
    /**
     * Load lazy image
     * @param {HTMLElement} element - Image element
     */
    loadLazyImage(element) {
        if (element.dataset.src) {
            element.src = element.dataset.src;
            element.removeAttribute('data-src');
        }
        
        if (element.dataset.srcset) {
            element.srcset = element.dataset.srcset;
            element.removeAttribute('data-srcset');
        }
        
        element.classList.add('loaded');
    }
    
    /**
     * Set up mutation observer for dynamic content
     */
    setupMutationObserver() {
        const mutationObserver = new MutationObserver((mutations) => {
            mutations.forEach(mutation => {
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        this.observeNewLazyElements(node);
                    }
                });
            });
        });
        
        mutationObserver.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        this.observers.set('mutation', mutationObserver);
    }
    
    /**
     * Observe new lazy elements
     * @param {HTMLElement} element - Element to check
     */
    observeNewLazyElements(element) {
        const lazyImages = element.querySelectorAll('img[data-src], [data-lazy]');
        const observer = this.observers.get('lazyImages');
        
        if (observer && lazyImages.length > 0) {
            lazyImages.forEach(img => observer.observe(img));
        }
    }
    
    /**
     * Set up resource optimization
     */
    setupResourceOptimization() {
        // Preload critical resources
        this.preloadCriticalResources();
        
        // Set up resource hints
        this.setupResourceHints();
        
        // Optimize images
        this.optimizeImages();
    }
    
    /**
     * Preload critical resources
     */
    preloadCriticalResources() {
        const criticalResources = [
            { href: 'styles.css', as: 'style' },
            { href: 'data/events.js', as: 'script' }
        ];
        
        criticalResources.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = resource.href;
            link.as = resource.as;
            document.head.appendChild(link);
        });
    }
    
    /**
     * Set up resource hints
     */
    setupResourceHints() {
        // DNS prefetch for external domains
        const externalDomains = [
            'fonts.googleapis.com',
            'fonts.gstatic.com'
        ];
        
        externalDomains.forEach(domain => {
            const link = document.createElement('link');
            link.rel = 'dns-prefetch';
            link.href = `//${domain}`;
            document.head.appendChild(link);
        });
    }
    
    /**
     * Optimize images
     */
    optimizeImages() {
        const images = document.querySelectorAll('img');
        
        images.forEach(img => {
            // Add loading="lazy" for modern browsers
            if (!img.hasAttribute('loading')) {
                img.loading = 'lazy';
            }
            
            // Add decoding="async" for better performance
            if (!img.hasAttribute('decoding')) {
                img.decoding = 'async';
            }
        });
    }
    
    /**
     * Monitor Core Web Vitals
     */
    monitorCoreWebVitals() {
        // Monitor Largest Contentful Paint (LCP)
        this.monitorLCP();
        
        // Monitor First Input Delay (FID)
        this.monitorFID();
        
        // Monitor Cumulative Layout Shift (CLS)
        this.monitorCLS();
    }
    
    /**
     * Monitor Largest Contentful Paint
     */
    monitorLCP() {
        if ('PerformanceObserver' in window) {
            try {
                const lcpObserver = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    const lastEntry = entries[entries.length - 1];
                    
                    this.addPerformanceEntry({
                        type: 'lcp',
                        value: lastEntry.startTime,
                        element: lastEntry.element?.tagName || 'unknown'
                    });
                });
                
                lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
                this.observers.set('lcp', lcpObserver);
            } catch (error) {
                console.warn('LCP monitoring failed:', error);
            }
        }
    }
    
    /**
     * Monitor First Input Delay
     */
    monitorFID() {
        if ('PerformanceObserver' in window) {
            try {
                const fidObserver = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    entries.forEach(entry => {
                        this.addPerformanceEntry({
                            type: 'fid',
                            value: entry.processingStart - entry.startTime,
                            inputType: entry.name
                        });
                    });
                });
                
                fidObserver.observe({ entryTypes: ['first-input'] });
                this.observers.set('fid', fidObserver);
            } catch (error) {
                console.warn('FID monitoring failed:', error);
            }
        }
    }
    
    /**
     * Monitor Cumulative Layout Shift
     */
    monitorCLS() {
        if ('PerformanceObserver' in window) {
            try {
                let clsValue = 0;
                
                const clsObserver = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    entries.forEach(entry => {
                        if (!entry.hadRecentInput) {
                            clsValue += entry.value;
                        }
                    });
                    
                    this.addPerformanceEntry({
                        type: 'cls',
                        value: clsValue
                    });
                });
                
                clsObserver.observe({ entryTypes: ['layout-shift'] });
                this.observers.set('cls', clsObserver);
            } catch (error) {
                console.warn('CLS monitoring failed:', error);
            }
        }
    }
    
    /**
     * Monitor memory usage
     */
    monitorMemoryUsage() {
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                this.metrics.memoryUsage = memory.usedJSHeapSize;
                
                // Warn if memory usage is high
                if (memory.usedJSHeapSize > memory.jsHeapSizeLimit * 0.9) {
                    console.warn('High memory usage detected');
                    this.dispatchPerformanceEvent('performance:high-memory', {
                        used: memory.usedJSHeapSize,
                        limit: memory.jsHeapSizeLimit
                    });
                }
            }, 30000); // Check every 30 seconds
        }
    }
    
    /**
     * Add performance entry
     * @param {Object} entry - Performance entry
     */
    addPerformanceEntry(entry) {
        entry.timestamp = Date.now();
        this.performanceEntries.push(entry);
        
        // Maintain entries list size
        if (this.performanceEntries.length > this.maxEntries) {
            this.performanceEntries.shift();
        }
    }
    
    /**
     * Get performance metrics
     * @returns {Object} Performance metrics
     */
    getMetrics() {
        return {
            ...this.metrics,
            entries: this.performanceEntries.slice(-10), // Last 10 entries
            timestamp: Date.now()
        };
    }
    
    /**
     * Get performance report
     * @returns {Object} Performance report
     */
    getPerformanceReport() {
        const report = {
            metrics: this.getMetrics(),
            coreWebVitals: this.getCoreWebVitals(),
            resourceTiming: this.getResourceTiming(),
            recommendations: this.getPerformanceRecommendations()
        };
        
        return report;
    }
    
    /**
     * Get Core Web Vitals
     * @returns {Object} Core Web Vitals data
     */
    getCoreWebVitals() {
        const lcp = this.performanceEntries.filter(e => e.type === 'lcp').pop();
        const fid = this.performanceEntries.filter(e => e.type === 'fid').pop();
        const cls = this.performanceEntries.filter(e => e.type === 'cls').pop();
        
        return {
            lcp: lcp?.value || null,
            fid: fid?.value || null,
            cls: cls?.value || null
        };
    }
    
    /**
     * Get resource timing data
     * @returns {Array} Resource timing entries
     */
    getResourceTiming() {
        return this.performanceEntries.filter(e => e.type === 'slow-resource');
    }
    
    /**
     * Get performance recommendations
     * @returns {Array} Performance recommendations
     */
    getPerformanceRecommendations() {
        const recommendations = [];
        const cwv = this.getCoreWebVitals();
        
        // LCP recommendations
        if (cwv.lcp && cwv.lcp > 2500) {
            recommendations.push({
                type: 'lcp',
                message: 'Largest Contentful Paint is slow. Consider optimizing images and critical resources.',
                priority: 'high'
            });
        }
        
        // FID recommendations
        if (cwv.fid && cwv.fid > 100) {
            recommendations.push({
                type: 'fid',
                message: 'First Input Delay is high. Consider reducing JavaScript execution time.',
                priority: 'medium'
            });
        }
        
        // CLS recommendations
        if (cwv.cls && cwv.cls > 0.1) {
            recommendations.push({
                type: 'cls',
                message: 'Cumulative Layout Shift is high. Ensure images and ads have dimensions.',
                priority: 'high'
            });
        }
        
        return recommendations;
    }
    
    /**
     * Dispatch performance event
     * @param {string} eventType - Event type
     * @param {Object} detail - Event detail
     */
    dispatchPerformanceEvent(eventType, detail = {}) {
        const event = new CustomEvent(eventType, { detail });
        window.dispatchEvent(event);
    }
    
    /**
     * Destroy performance manager
     */
    destroy() {
        // Disconnect all observers
        this.observers.forEach(observer => {
            if (observer && typeof observer.disconnect === 'function') {
                observer.disconnect();
            }
        });
        
        this.observers.clear();
        this.performanceEntries = [];
        this.isInitialized = false;
        
        console.log('Performance manager destroyed');
    }
}

// Create singleton instance
const performanceManager = new PerformanceManager();

// Export for module use
export default performanceManager;
