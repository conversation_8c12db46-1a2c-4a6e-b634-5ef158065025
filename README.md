# 人类简史互动学习平台

基于《人类简史》构建的现代化互动学习平台，专注于人类认知革命的历史进程。

## 🌟 特性

### 核心功能
- **互动时间轴**: 可视化历史事件时间线，支持点击导航
- **多维度分析**: 从哲学、心理学、社会学、经济学角度分析历史事件
- **智能搜索**: 全文搜索历史事件，支持关键词高亮
- **测验系统**: 交互式问答测试，巩固学习效果
- **学习进度**: 跟踪学习进度，记录测验成绩

### 技术特性
- **模块化架构**: ES6模块系统，清晰的代码组织
- **响应式设计**: 适配桌面和移动设备
- **无障碍支持**: 符合WCAG标准，支持屏幕阅读器
- **性能优化**: 懒加载、资源预加载、Core Web Vitals监控
- **主题切换**: 支持浅色/深色主题，跟随系统偏好
- **错误处理**: 全面的错误边界和用户友好的错误提示

## 🚀 快速开始

### 环境要求
- 现代浏览器（支持ES6模块）
- Python 3.x（用于本地服务器）
- Node.js 14+（用于开发工具）

### 安装和运行

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd history-learning-platform
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **启动开发服务器**
   ```bash
   npm run dev
   ```
   或者使用Python：
   ```bash
   python -m http.server 8000
   ```

4. **访问应用**
   打开浏览器访问 `http://localhost:8000`

## 📁 项目结构

```
├── index.html              # 主页面
├── app.js                  # 应用程序入口
├── styles.css              # 样式文件
├── package.json            # 项目配置
├── eslint.config.js        # ESLint配置
├── data/                   # 数据文件
│   └── events.js          # 历史事件数据
├── scripts/               # JavaScript模块
│   └── modules/           # 功能模块
│       ├── error-handling.js      # 错误处理
│       ├── theme-manager.js       # 主题管理
│       ├── search.js              # 搜索引擎
│       ├── timeline.js            # 时间轴
│       ├── event-rendering.js     # 事件渲染
│       ├── analysis.js            # 分析面板
│       ├── quiz.js                # 测验系统
│       ├── progress-tracker.js    # 进度跟踪
│       ├── performance.js         # 性能监控
│       ├── accessibility.js       # 无障碍功能
│       ├── security.js            # 安全管理
│       └── data-validator.js      # 数据验证
└── docs/                  # 文档
```

## 🛠️ 开发指南

### 代码规范
- 使用ES6+语法和模块系统
- 遵循ESLint配置的代码风格
- 使用JSDoc注释文档化函数
- 采用单一职责原则设计模块

### 模块系统
每个功能模块都是独立的ES6模块，包含：
- 类定义和方法
- 初始化和销毁逻辑
- 事件监听和派发
- 错误处理

### 添加新功能
1. 在`scripts/modules/`创建新模块文件
2. 实现模块类和导出
3. 在`app.js`中导入和初始化
4. 添加相应的样式和HTML结构

### 数据格式
历史事件数据格式：
```javascript
{
  id: 1,
  title: "事件标题",
  period: "时期描述",
  region: "地区",
  description: "详细描述",
  philosophy: { content: "哲学分析", questions: ["思考问题"] },
  psychology: { content: "心理学分析", questions: [] },
  sociology: { content: "社会学分析", questions: [] },
  economics: { content: "经济分析", questions: [] },
  quiz: {
    questions: [
      {
        question: "问题",
        options: ["选项1", "选项2", "选项3", "选项4"],
        answer: 0
      }
    ]
  }
}
```

## 🎨 样式和主题

### CSS架构
- 使用CSS自定义属性（CSS变量）
- 响应式设计，移动优先
- 模块化CSS，按功能组织

### 主题系统
- 支持浅色/深色主题
- 自动检测系统偏好
- 主题切换动画（可关闭）

### 无障碍设计
- 语义化HTML结构
- ARIA标签和角色
- 键盘导航支持
- 屏幕阅读器优化
- 高对比度模式

## 🔧 配置

### ESLint配置
项目使用ESLint进行代码质量检查：
```bash
npm run lint        # 检查代码
npm run lint:fix    # 自动修复问题
```

### 性能监控
应用内置性能监控，包括：
- Core Web Vitals (LCP, FID, CLS)
- 资源加载时间
- 内存使用情况
- 用户交互延迟

## 📱 浏览器支持

### 最低要求
- Chrome 61+
- Firefox 60+
- Safari 11+
- Edge 79+

### 功能支持
- ES6模块：所有现代浏览器
- CSS Grid：IE11需要前缀
- Intersection Observer：需要polyfill（IE）
- Performance Observer：现代浏览器

## 🚀 部署

### 生产环境部署
1. **构建优化**（未来版本）
   ```bash
   npm run build
   ```

2. **静态文件服务**
   - 可部署到任何静态文件服务器
   - 推荐使用HTTPS
   - 配置适当的缓存策略

3. **CDN优化**
   - 静态资源使用CDN
   - 启用Gzip压缩
   - 设置适当的缓存头

### 安全配置
- 配置Content Security Policy (CSP)
- 启用HTTPS
- 设置安全响应头
- 输入验证和XSS防护

## 🤝 贡献指南

### 提交代码
1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

### 代码审查
- 确保通过ESLint检查
- 添加适当的测试
- 更新相关文档
- 遵循现有代码风格

## 📄 许可证

MIT License - 详见LICENSE文件

## 🙏 致谢

- 基于尤瓦尔·赫拉利的《人类简史》
- 使用现代Web技术构建
- 感谢所有贡献者

## 📞 联系方式

- 项目主页：[GitHub Repository]
- 问题反馈：[GitHub Issues]
- 邮箱：[<EMAIL>]

---

**注意**: 这是一个教育项目，旨在通过互动方式学习人类历史。内容基于《人类简史》一书，仅供学习参考。
