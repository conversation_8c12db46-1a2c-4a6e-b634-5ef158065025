// 初始化地图交互
function setupMapInteractions() {
    try {
        const map = document.getElementById('interactive-map');
        const tooltip = document.getElementById('region-tooltip');
        
        if (!map || !tooltip) {
            console.warn('Map elements not found');
            return;
        }
        
        // 为每个区域添加交互功能
        const regions = map.querySelectorAll('.map-region');
        regions.forEach(region => {
            // 鼠标悬停显示提示
            region.addEventListener('mouseenter', (e) => {
                const regionName = e.target.getAttribute('data-region');
                tooltip.textContent = `点击查看${regionName}历史事件`;
                tooltip.style.left = `${e.pageX + 10}px`;
                tooltip.style.top = `${e.pageY + 10}px`;
                tooltip.style.display = 'block';
            });
            
            region.addEventListener('mousemove', (e) => {
                tooltip.style.left = `${e.pageX + 10}px`;
                tooltip.style.top = `${e.pageY + 10}px`;
            });
            
            region.addEventListener('mouseleave', () => {
                tooltip.style.display = 'none';
            });
            
            // 点击区域显示相关事件
            region.addEventListener('click', (e) => {
                const regionName = e.target.getAttribute('data-region');
                filterEventsByRegion(regionName);
                
                // 滚动到事件列表部分
                const eventListSection = document.getElementById('event-list-section');
                if (eventListSection) {
                    eventListSection.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });
        
        // 添加地区筛选功能
        const regionFilter = document.getElementById('region-filter');
        if (regionFilter) {
            regionFilter.addEventListener('change', (e) => {
                const selectedRegion = e.target.value;
                filterEventsByRegion(selectedRegion);
            });
        }
        
        // 添加图片画廊的地区筛选功能
        const regionImageFilter = document.getElementById('region-image-filter');
        if (regionImageFilter) {
            regionImageFilter.addEventListener('change', () => {
                // 这里将实现图片画廊的筛选功能
                console.log('Region image filter changed');
            });
        }
        
        // 添加时间筛选功能
        const yearImageFilter = document.getElementById('year-image-filter');
        if (yearImageFilter) {
            yearImageFilter.addEventListener('change', () => {
                // 这里将实现按时期筛选图片的功能
                console.log('Year image filter changed');
            });
        }
        
        // 添加图片搜索功能
        const imageSearch = document.getElementById('image-search');
        if (imageSearch) {
            imageSearch.addEventListener('input', () => {
                // 这里将实现图片搜索功能
                console.log('Image search input');
            });
        }
    } catch (error) {
        console.error('Failed to setup map interactions:', error);
    }
}

// 根据地区筛选事件
function filterEventsByRegion(region) {
    if (!allHistoricalEvents || allHistoricalEvents.length === 0) {
        return;
    }
    
    let filteredEvents = [];
    
    if (region === 'all') {
        filteredEvents = allHistoricalEvents;
    } else {
        filteredEvents = allHistoricalEvents.filter(event => {
            // 如果事件本身有region属性，直接匹配
            if (event.region && event.region.toLowerCase() === region) {
                return true;
            }
            
            // 否则检查多维分析维度中的地区信息
            const analysisDimensions = ['philosophy', 'psychology', 'sociology', 'economics'];
            return analysisDimensions.some(dim => {
                if (event[dim] && event[dim].glossaryTerms && typeof event[dim].glossaryTerms === 'object') {
                    // 检查术语表中的地区信息
                    return Object.entries(event[dim].glossaryTerms).some(([term, definition]) => 
                        definition.toLowerCase().includes(region)
                    );
                }
                return false;
            });
        });
    }
    
    // 更新事件列表显示
    updateEventDisplay(filteredEvents);
    
    // 触发自定义事件，通知其他组件事件已过滤
    const event = new Event('eventsFiltered');
    document.dispatchEvent(event);
}
