// 修复：完成 initQuizStatistics 函数定义
function initQuizStatistics() {
    try {
        // 从localStorage加载测验结果
        const quizResults = getQuizResults();
        
        // 检查是否存在测验容器
        const quizContainers = document.querySelectorAll('.quiz-container');
        if (!quizContainers || quizContainers.length === 0) {
            return;
        }
        
        // 为每个测验添加提交事件监听器
        quizContainers.forEach(container => {
            const eventIdStr = container.id.replace('quiz-', '');
            const eventId = parseInt(eventIdStr);
            
            // 验证事件ID
            if (isNaN(eventId) || eventId <= 0) {
                console.warn(`Invalid event ID: ${eventIdStr}`);
                return;
            }
            
            const submitBtn = container.querySelector('.submit-quiz');
            if (!submitBtn) {
                console.warn(`Submit button not found for event ${eventId}`);
                return;
            }
            
            submitBtn.addEventListener('click', () => {
                // 获取用户的答案
                const scoreElement = container.querySelector('.result p');
                if (scoreElement && scoreElement.textContent.includes('答对')) {
                    const scoreMatch = scoreElement.textContent.match(/(\d+) /);
                    if (scoreMatch && scoreMatch[1]) {
                        const score = parseInt(scoreMatch[1]);
                        
                        // 更新测验结果
                        updateQuizResult(eventId, score);
                        
                        // 更新测验统计显示
                        updateQuizStatisticsDisplay(eventId, score);
                        
                        // 如果学习进度未标记为已读，自动标记为已读
                        const readEvents = getReadEvents();
                        if (!readEvents.includes(eventId)) {
                            updateLearningProgress(eventId);
                        }
                    }
                }
            });
        });
        
        // 初始化测验统计显示
        initQuizStatisticsDisplay();
        
        // 检查现有测验结果并显示总结
        Object.keys(quizResults).forEach(eventIdStr => {
            const eventId = parseInt(eventIdStr);
            if (isNaN(eventId) || eventId <= 0) {
                console.warn(`Invalid event ID in results: ${eventIdStr}`);
                return;
            }
            
            const score = quizResults[eventId]?.score || 0;
            if (score > 0) {
                showQuizSummary(eventId, score);
            }
        });
    } catch (error) {
        console.error('Quiz statistics initialization error:', error);
        // 不抛出错误，因为测验统计是辅助功能
    }
}