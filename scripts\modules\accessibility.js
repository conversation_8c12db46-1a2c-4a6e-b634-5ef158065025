/**
 * Accessibility Module
 * Provides accessibility enhancements and WCAG compliance utilities
 */

class AccessibilityManager {
    constructor() {
        this.isInitialized = false;
        this.focusTracker = null;
        this.announcer = null;
        this.keyboardNavigation = null;
        this.colorContrastChecker = null;
        
        // Accessibility preferences
        this.preferences = {
            reducedMotion: false,
            highContrast: false,
            largeText: false,
            screenReader: false
        };
        
        // Keyboard navigation state
        this.keyboardNavigationActive = false;
        this.focusableElements = [];
        this.currentFocusIndex = -1;
    }
    
    /**
     * Initialize accessibility manager
     */
    init() {
        if (this.isInitialized) return;
        
        // Detect user preferences
        this.detectUserPreferences();
        
        // Set up screen reader announcements
        this.setupScreenReaderSupport();
        
        // Set up keyboard navigation
        this.setupKeyboardNavigation();
        
        // Set up focus management
        this.setupFocusManagement();
        
        // Set up ARIA enhancements
        this.setupARIAEnhancements();
        
        // Set up color contrast checking
        this.setupColorContrastChecking();
        
        // Set up skip links
        this.setupSkipLinks();
        
        this.isInitialized = true;
        console.log('Accessibility manager initialized');
    }
    
    /**
     * Detect user accessibility preferences
     */
    detectUserPreferences() {
        // Check for reduced motion preference
        if (window.matchMedia) {
            const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
            this.preferences.reducedMotion = reducedMotionQuery.matches;
            
            reducedMotionQuery.addEventListener('change', (e) => {
                this.preferences.reducedMotion = e.matches;
                this.applyMotionPreferences();
            });
            
            // Check for high contrast preference
            const highContrastQuery = window.matchMedia('(prefers-contrast: high)');
            this.preferences.highContrast = highContrastQuery.matches;
            
            highContrastQuery.addEventListener('change', (e) => {
                this.preferences.highContrast = e.matches;
                this.applyContrastPreferences();
            });
        }
        
        // Detect screen reader
        this.detectScreenReader();
        
        // Apply initial preferences
        this.applyAccessibilityPreferences();
    }
    
    /**
     * Detect screen reader usage
     */
    detectScreenReader() {
        // Check for common screen reader indicators
        const indicators = [
            navigator.userAgent.includes('NVDA'),
            navigator.userAgent.includes('JAWS'),
            navigator.userAgent.includes('VoiceOver'),
            window.speechSynthesis !== undefined,
            document.querySelector('[aria-live]') !== null
        ];
        
        this.preferences.screenReader = indicators.some(indicator => indicator);
    }
    
    /**
     * Set up screen reader support
     */
    setupScreenReaderSupport() {
        // Create live region for announcements
        this.announcer = document.createElement('div');
        this.announcer.setAttribute('aria-live', 'polite');
        this.announcer.setAttribute('aria-atomic', 'true');
        this.announcer.className = 'sr-only';
        this.announcer.style.cssText = `
            position: absolute !important;
            width: 1px !important;
            height: 1px !important;
            padding: 0 !important;
            margin: -1px !important;
            overflow: hidden !important;
            clip: rect(0, 0, 0, 0) !important;
            white-space: nowrap !important;
            border: 0 !important;
        `;
        document.body.appendChild(this.announcer);
        
        // Set up event listeners for announcements
        this.setupAnnouncementListeners();
    }
    
    /**
     * Set up announcement listeners
     */
    setupAnnouncementListeners() {
        // Announce page changes
        window.addEventListener('popstate', () => {
            this.announce('页面已更改');
        });
        
        // Announce content updates
        window.addEventListener('events:rendered', () => {
            this.announce('历史事件列表已更新');
        });
        
        // Announce search results
        window.addEventListener('search:results-updated', (e) => {
            const count = e.detail.count || 0;
            this.announce(`搜索完成，找到 ${count} 个结果`);
        });
        
        // Announce quiz completion
        window.addEventListener('quiz:completed', (e) => {
            const score = e.detail.score || 0;
            this.announce(`测验完成，得分 ${score} 分`);
        });
    }
    
    /**
     * Announce message to screen readers
     * @param {string} message - Message to announce
     * @param {string} priority - Announcement priority ('polite' or 'assertive')
     */
    announce(message, priority = 'polite') {
        if (!this.announcer) return;
        
        this.announcer.setAttribute('aria-live', priority);
        this.announcer.textContent = message;
        
        // Clear after announcement
        setTimeout(() => {
            this.announcer.textContent = '';
        }, 1000);
    }
    
    /**
     * Set up keyboard navigation
     */
    setupKeyboardNavigation() {
        // Track keyboard usage
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                this.keyboardNavigationActive = true;
                document.body.classList.add('keyboard-navigation');
            }
        });
        
        // Track mouse usage
        document.addEventListener('mousedown', () => {
            this.keyboardNavigationActive = false;
            document.body.classList.remove('keyboard-navigation');
        });
        
        // Set up custom keyboard shortcuts
        this.setupKeyboardShortcuts();
    }
    
    /**
     * Set up keyboard shortcuts
     */
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Skip if user is typing in an input
            if (e.target.matches('input, textarea, select')) {
                return;
            }
            
            // Handle keyboard shortcuts
            switch (e.key) {
                case '/':
                    e.preventDefault();
                    this.focusSearchInput();
                    break;
                    
                case 'Escape':
                    this.handleEscapeKey();
                    break;
                    
                case 'h':
                    if (e.altKey) {
                        e.preventDefault();
                        this.focusMainHeading();
                    }
                    break;
                    
                case 'm':
                    if (e.altKey) {
                        e.preventDefault();
                        this.focusMainContent();
                    }
                    break;
            }
        });
    }
    
    /**
     * Focus search input
     */
    focusSearchInput() {
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            searchInput.focus();
            this.announce('搜索框已聚焦');
        }
    }
    
    /**
     * Handle escape key
     */
    handleEscapeKey() {
        // Close any open modals or dropdowns
        const openElements = document.querySelectorAll('[aria-expanded="true"]');
        openElements.forEach(element => {
            element.setAttribute('aria-expanded', 'false');
        });
        
        // Hide search results
        const searchResults = document.getElementById('search-results');
        if (searchResults) {
            searchResults.style.display = 'none';
        }
        
        // Return focus to main content
        this.focusMainContent();
    }
    
    /**
     * Focus main heading
     */
    focusMainHeading() {
        const mainHeading = document.querySelector('h1');
        if (mainHeading) {
            mainHeading.focus();
            this.announce('主标题已聚焦');
        }
    }
    
    /**
     * Focus main content
     */
    focusMainContent() {
        const mainContent = document.getElementById('main-content');
        if (mainContent) {
            mainContent.focus();
            this.announce('主要内容已聚焦');
        }
    }
    
    /**
     * Set up focus management
     */
    setupFocusManagement() {
        // Track focus changes
        document.addEventListener('focusin', (e) => {
            this.handleFocusChange(e.target);
        });
        
        // Trap focus in modals
        this.setupFocusTrapping();
        
        // Restore focus after interactions
        this.setupFocusRestoration();
    }
    
    /**
     * Handle focus change
     * @param {HTMLElement} element - Focused element
     */
    handleFocusChange(element) {
        // Update focus tracker
        this.focusTracker = element;
        
        // Ensure focused element is visible
        this.ensureElementVisible(element);
        
        // Update focus indicators
        this.updateFocusIndicators(element);
    }
    
    /**
     * Ensure element is visible
     * @param {HTMLElement} element - Element to make visible
     */
    ensureElementVisible(element) {
        if (!element) return;
        
        // Scroll element into view if needed
        const rect = element.getBoundingClientRect();
        const isVisible = rect.top >= 0 && rect.bottom <= window.innerHeight;
        
        if (!isVisible) {
            element.scrollIntoView({
                behavior: this.preferences.reducedMotion ? 'auto' : 'smooth',
                block: 'center'
            });
        }
    }
    
    /**
     * Update focus indicators
     * @param {HTMLElement} element - Focused element
     */
    updateFocusIndicators(element) {
        // Remove previous focus indicators
        document.querySelectorAll('.focus-indicator').forEach(indicator => {
            indicator.remove();
        });
        
        // Add focus indicator if keyboard navigation is active
        if (this.keyboardNavigationActive && element) {
            const indicator = document.createElement('div');
            indicator.className = 'focus-indicator';
            indicator.setAttribute('aria-hidden', 'true');
            
            // Position indicator around focused element
            const rect = element.getBoundingClientRect();
            indicator.style.cssText = `
                position: fixed;
                top: ${rect.top - 2}px;
                left: ${rect.left - 2}px;
                width: ${rect.width + 4}px;
                height: ${rect.height + 4}px;
                border: 2px solid #0066cc;
                border-radius: 4px;
                pointer-events: none;
                z-index: 9999;
            `;
            
            document.body.appendChild(indicator);
        }
    }
    
    /**
     * Set up focus trapping for modals
     */
    setupFocusTrapping() {
        // This will be implemented when modal components are added
        console.log('Focus trapping setup ready');
    }
    
    /**
     * Set up focus restoration
     */
    setupFocusRestoration() {
        // Store focus before navigation
        window.addEventListener('beforeunload', () => {
            if (this.focusTracker) {
                sessionStorage.setItem('lastFocusedElement', this.focusTracker.id || '');
            }
        });
        
        // Restore focus after page load
        window.addEventListener('load', () => {
            const lastFocusedId = sessionStorage.getItem('lastFocusedElement');
            if (lastFocusedId) {
                const element = document.getElementById(lastFocusedId);
                if (element) {
                    setTimeout(() => element.focus(), 100);
                }
                sessionStorage.removeItem('lastFocusedElement');
            }
        });
    }
    
    /**
     * Set up ARIA enhancements
     */
    setupARIAEnhancements() {
        // Add missing ARIA labels
        this.addMissingARIALabels();
        
        // Set up live regions
        this.setupLiveRegions();
        
        // Enhance form accessibility
        this.enhanceFormAccessibility();
        
        // Set up landmark roles
        this.setupLandmarkRoles();
    }
    
    /**
     * Add missing ARIA labels
     */
    addMissingARIALabels() {
        // Add labels to buttons without text
        const unlabeledButtons = document.querySelectorAll('button:not([aria-label]):not([aria-labelledby])');
        unlabeledButtons.forEach(button => {
            if (!button.textContent.trim()) {
                button.setAttribute('aria-label', '按钮');
            }
        });
        
        // Add labels to form controls
        const unlabeledInputs = document.querySelectorAll('input:not([aria-label]):not([aria-labelledby])');
        unlabeledInputs.forEach(input => {
            const label = document.querySelector(`label[for="${input.id}"]`);
            if (!label && input.placeholder) {
                input.setAttribute('aria-label', input.placeholder);
            }
        });
    }
    
    /**
     * Set up live regions
     */
    setupLiveRegions() {
        // Add aria-live to dynamic content areas
        const dynamicAreas = document.querySelectorAll('.event-list, .search-results, .analysis-content');
        dynamicAreas.forEach(area => {
            if (!area.hasAttribute('aria-live')) {
                area.setAttribute('aria-live', 'polite');
            }
        });
    }
    
    /**
     * Enhance form accessibility
     */
    enhanceFormAccessibility() {
        // Add required indicators
        const requiredFields = document.querySelectorAll('input[required], textarea[required], select[required]');
        requiredFields.forEach(field => {
            if (!field.hasAttribute('aria-required')) {
                field.setAttribute('aria-required', 'true');
            }
        });
        
        // Add error associations
        const errorMessages = document.querySelectorAll('.error-message');
        errorMessages.forEach(error => {
            const fieldId = error.dataset.for;
            if (fieldId) {
                const field = document.getElementById(fieldId);
                if (field) {
                    field.setAttribute('aria-describedby', error.id);
                    field.setAttribute('aria-invalid', 'true');
                }
            }
        });
    }
    
    /**
     * Set up landmark roles
     */
    setupLandmarkRoles() {
        // Ensure main content has proper role
        const mainContent = document.getElementById('main-content');
        if (mainContent && !mainContent.hasAttribute('role')) {
            mainContent.setAttribute('role', 'main');
        }
        
        // Add navigation role to nav elements
        const navElements = document.querySelectorAll('nav:not([role])');
        navElements.forEach(nav => {
            nav.setAttribute('role', 'navigation');
        });
    }
    
    /**
     * Set up color contrast checking
     */
    setupColorContrastChecking() {
        // This would typically integrate with a color contrast library
        // For now, we'll add basic contrast warnings
        this.checkBasicContrast();
    }
    
    /**
     * Check basic color contrast
     */
    checkBasicContrast() {
        // Add warning for potential contrast issues
        const lightText = document.querySelectorAll('.light-text, .muted-text');
        lightText.forEach(element => {
            element.setAttribute('data-contrast-warning', 'true');
        });
    }
    
    /**
     * Set up skip links
     */
    setupSkipLinks() {
        // Skip links are already in HTML, just ensure they work
        const skipLinks = document.querySelectorAll('.skip-link');
        skipLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href').substring(1);
                const target = document.getElementById(targetId);
                if (target) {
                    target.focus();
                    target.scrollIntoView();
                }
            });
        });
    }
    
    /**
     * Apply accessibility preferences
     */
    applyAccessibilityPreferences() {
        this.applyMotionPreferences();
        this.applyContrastPreferences();
    }
    
    /**
     * Apply motion preferences
     */
    applyMotionPreferences() {
        if (this.preferences.reducedMotion) {
            document.body.classList.add('reduced-motion');
        } else {
            document.body.classList.remove('reduced-motion');
        }
    }
    
    /**
     * Apply contrast preferences
     */
    applyContrastPreferences() {
        if (this.preferences.highContrast) {
            document.body.classList.add('high-contrast');
        } else {
            document.body.classList.remove('high-contrast');
        }
    }
    
    /**
     * Get accessibility report
     * @returns {Object} Accessibility report
     */
    getAccessibilityReport() {
        return {
            preferences: this.preferences,
            keyboardNavigationActive: this.keyboardNavigationActive,
            focusedElement: this.focusTracker?.tagName || null,
            timestamp: Date.now()
        };
    }
    
    /**
     * Destroy accessibility manager
     */
    destroy() {
        if (this.announcer) {
            this.announcer.remove();
        }
        
        // Remove focus indicators
        document.querySelectorAll('.focus-indicator').forEach(indicator => {
            indicator.remove();
        });
        
        this.isInitialized = false;
        console.log('Accessibility manager destroyed');
    }
}

// Create singleton instance
const accessibilityManager = new AccessibilityManager();

// Export for module use
export default accessibilityManager;
