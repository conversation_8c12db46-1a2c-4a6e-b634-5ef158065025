# 开发者文档

## 架构概述

### 应用程序架构
人类简史学习平台采用模块化架构，基于ES6模块系统构建。

```
┌─────────────────┐
│   index.html    │  ← 入口页面
└─────────────────┘
         │
┌─────────────────┐
│     app.js      │  ← 应用程序主控制器
└─────────────────┘
         │
┌─────────────────┐
│   Core Modules  │  ← 核心模块
├─────────────────┤
│ • ErrorHandler  │
│ • ThemeManager  │
│ • Performance   │
│ • Accessibility │
│ • Security      │
└─────────────────┘
         │
┌─────────────────┐
│Feature Modules  │  ← 功能模块
├─────────────────┤
│ • EventRenderer │
│ • Timeline      │
│ • SearchEngine  │
│ • QuizEngine    │
│ • ProgressTracker│
└─────────────────┘
```

### 模块设计原则

1. **单一职责**: 每个模块负责一个特定功能
2. **松耦合**: 模块间通过事件通信，减少直接依赖
3. **高内聚**: 相关功能集中在同一模块内
4. **可测试**: 每个模块都可以独立测试
5. **可扩展**: 新功能可以通过添加模块实现

## 核心模块详解

### 1. 应用程序控制器 (app.js)

**职责**: 
- 模块加载和初始化
- 全局事件协调
- 应用程序生命周期管理

**关键方法**:
```javascript
class HistoryLearningApp {
    async init()                    // 初始化应用程序
    async loadFeatureModules()      // 加载功能模块
    setupGlobalEventListeners()    // 设置全局事件监听
    getModule(name)                 // 获取模块实例
    shutdown()                      // 优雅关闭
}
```

### 2. 错误处理模块 (error-handling.js)

**职责**:
- 全局错误捕获和处理
- 用户友好的错误提示
- 错误日志记录和分析

**特性**:
- JavaScript错误捕获
- Promise rejection处理
- 资源加载错误监控
- CSP违规报告

### 3. 主题管理模块 (theme-manager.js)

**职责**:
- 主题切换功能
- 系统偏好检测
- 主题持久化存储

**支持的主题**:
- 浅色主题 (默认)
- 深色主题
- 系统自动切换

### 4. 性能监控模块 (performance.js)

**职责**:
- Core Web Vitals监控
- 资源加载性能分析
- 内存使用监控
- 懒加载实现

**监控指标**:
- Largest Contentful Paint (LCP)
- First Input Delay (FID)
- Cumulative Layout Shift (CLS)
- 资源加载时间

### 5. 无障碍模块 (accessibility.js)

**职责**:
- WCAG合规性
- 键盘导航支持
- 屏幕阅读器优化
- 焦点管理

**功能**:
- 自动ARIA标签
- 键盘快捷键
- 实时公告
- 焦点陷阱

## 功能模块详解

### 1. 事件渲染模块 (event-rendering.js)

**职责**: 历史事件的显示和交互

**核心功能**:
```javascript
class EventRenderer {
    renderEventList(events)         // 渲染事件列表
    renderEventDetail(eventId)      // 渲染事件详情
    createEventCard(event)          // 创建事件卡片
    updateEventDisplay(events)      // 更新显示
}
```

### 2. 时间轴模块 (timeline.js)

**职责**: 时间轴可视化和导航

**特性**:
- 按时间顺序排列事件
- 交互式时间点
- 自动布局算法
- 响应式设计

### 3. 搜索引擎模块 (search.js)

**职责**: 全文搜索和结果展示

**搜索特性**:
- 实时搜索建议
- 关键词高亮
- 相关性排序
- 搜索历史

**搜索算法**:
```javascript
// 权重计算
title match: 10分
period match: 5分
region match: 3分
content match: 1分
```

### 4. 测验系统模块 (quiz.js)

**职责**: 交互式测验和评分

**测验类型**:
- 单选题
- 多选题
- 判断题
- 填空题（计划中）

### 5. 进度跟踪模块 (progress-tracker.js)

**职责**: 学习进度记录和统计

**跟踪数据**:
- 已读事件列表
- 测验成绩记录
- 学习时间统计
- 成就系统

## 数据流架构

### 事件驱动通信

模块间通过自定义事件进行通信：

```javascript
// 事件派发
window.dispatchEvent(new CustomEvent('search:result-selected', {
    detail: { eventId: 123 }
}));

// 事件监听
window.addEventListener('search:result-selected', (e) => {
    this.renderEventDetail(e.detail.eventId);
});
```

### 主要事件类型

| 事件名称 | 触发时机 | 数据 |
|---------|---------|------|
| `app:initialized` | 应用初始化完成 | - |
| `events:rendered` | 事件列表渲染完成 | `{count}` |
| `search:result-selected` | 搜索结果被选择 | `{eventId}` |
| `timeline:event-selected` | 时间轴事件被选择 | `{eventId}` |
| `quiz:completed` | 测验完成 | `{eventId, score}` |
| `progress:event-read` | 事件被标记为已读 | `{eventId}` |
| `themechange` | 主题切换 | `{oldTheme, newTheme}` |

## 数据管理

### 数据存储策略

1. **静态数据**: 历史事件数据存储在`data/events.js`
2. **用户数据**: 使用localStorage存储用户偏好和进度
3. **会话数据**: 使用sessionStorage存储临时状态

### 数据验证

使用`data-validator.js`模块进行数据验证：

```javascript
const result = dataValidator.validateEvent(event);
if (!result.isValid) {
    console.error('数据验证失败:', result.errors);
}
```

### 数据格式规范

#### 历史事件数据结构
```javascript
{
    id: number,                    // 唯一标识符
    title: string,                 // 事件标题
    period: string,                // 时期描述
    region?: string,               // 地区（可选）
    description: string,           // 详细描述
    
    // 多维度分析
    philosophy?: {
        content: string,
        questions?: string[]
    },
    psychology?: { /* 同上 */ },
    sociology?: { /* 同上 */ },
    economics?: { /* 同上 */ },
    
    // 测验数据
    quiz?: {
        questions: [{
            question: string,
            options: string[],
            answer: number
        }]
    }
}
```

## 样式架构

### CSS组织结构

```css
/* 1. CSS Reset和基础样式 */
/* 2. CSS自定义属性（变量） */
/* 3. 布局样式 */
/* 4. 组件样式 */
/* 5. 主题样式 */
/* 6. 响应式样式 */
/* 7. 无障碍样式 */
/* 8. 动画和过渡 */
```

### CSS变量系统

```css
:root {
    /* 颜色系统 */
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --background-color: #ffffff;
    --text-color: #2c3e50;
    
    /* 间距系统 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    
    /* 字体系统 */
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
}
```

## 性能优化策略

### 1. 代码分割
- ES6模块动态导入
- 按需加载功能模块
- 懒加载非关键资源

### 2. 资源优化
- 图片懒加载
- 资源预加载
- CDN加速

### 3. 渲染优化
- 虚拟滚动（大数据集）
- 防抖和节流
- 避免强制同步布局

### 4. 缓存策略
- 浏览器缓存
- Service Worker（计划中）
- 内存缓存

## 测试策略

### 单元测试
```javascript
// 示例：搜索模块测试
describe('SearchEngine', () => {
    test('should filter events by title', () => {
        const results = searchEngine.search('认知革命');
        expect(results.length).toBeGreaterThan(0);
        expect(results[0].title).toContain('认知革命');
    });
});
```

### 集成测试
- 模块间交互测试
- 事件流测试
- 数据流测试

### E2E测试
- 用户操作流程测试
- 跨浏览器兼容性测试
- 性能基准测试

## 部署和CI/CD

### 构建流程
1. 代码质量检查 (ESLint)
2. 单元测试执行
3. 资源优化和压缩
4. 生成部署包

### 部署策略
- 静态文件部署
- CDN配置
- 缓存策略设置
- 监控和日志配置

## 调试和开发工具

### 开发者工具
- Chrome DevTools集成
- 性能分析面板
- 无障碍审计工具
- 网络监控

### 调试技巧
```javascript
// 启用调试模式
window.DEBUG = true;

// 获取模块实例
const app = window.HistoryApp;
const searchEngine = app.getModule('searchEngine');

// 查看性能指标
const performance = app.getModule('performanceManager');
console.log(performance.getMetrics());
```

## 扩展指南

### 添加新模块
1. 创建模块文件
2. 实现模块类
3. 添加到应用程序
4. 更新文档

### 添加新功能
1. 确定功能归属模块
2. 实现功能逻辑
3. 添加事件通信
4. 更新UI和样式
5. 添加测试

### 国际化支持
- 文本外部化
- 多语言资源文件
- 动态语言切换
- RTL布局支持

## 最佳实践

### 代码规范
- 使用TypeScript类型注释
- 遵循ESLint规则
- 编写JSDoc文档
- 保持代码简洁

### 性能最佳实践
- 避免内存泄漏
- 优化DOM操作
- 使用requestAnimationFrame
- 监控性能指标

### 无障碍最佳实践
- 语义化HTML
- 键盘导航支持
- 屏幕阅读器优化
- 颜色对比度检查

### 安全最佳实践
- 输入验证和清理
- XSS防护
- CSP配置
- HTTPS使用
