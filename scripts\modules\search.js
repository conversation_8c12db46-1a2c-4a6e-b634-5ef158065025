/**
 * Search Engine Module
 * Provides intelligent search functionality across historical events
 */

class SearchEngine {
    constructor() {
        this.searchInput = null;
        this.searchResults = null;
        this.searchIndex = [];
        this.lastBuildTime = 0;
        this.indexTTL = 30000; // 30 seconds
        this.searchTimeout = null;
        this.isInitialized = false;
        this.selectedIndex = -1;
    }
    
    /**
     * Initialize search engine
     */
    init() {
        if (this.isInitialized) return;
        
        // Get DOM elements
        this.searchInput = document.getElementById('search-input');
        this.searchResults = document.getElementById('search-results');
        
        if (!this.searchInput || !this.searchResults) {
            console.warn('Search elements not found');
            return;
        }
        
        // Set up event listeners
        this.setupEventListeners();
        
        // Build initial search index
        this.buildSearchIndex();
        
        this.isInitialized = true;
        console.log('Search engine initialized');
    }
    
    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Input event for real-time search
        this.searchInput.addEventListener('input', (e) => {
            this.handleSearchInput(e.target.value);
        });
        
        // Keyboard navigation
        this.searchInput.addEventListener('keydown', (e) => {
            this.handleKeyboardNavigation(e);
        });
        
        // Focus events
        this.searchInput.addEventListener('focus', () => {
            if (this.searchInput.value.trim().length >= 2) {
                this.searchResults.style.display = 'block';
            }
        });
        
        // Click outside to close
        document.addEventListener('click', (e) => {
            if (!this.searchInput.contains(e.target) && !this.searchResults.contains(e.target)) {
                this.hideResults();
            }
        });
        
        // Clear button
        const clearButton = this.searchInput.parentElement.querySelector('.search-clear');
        if (clearButton) {
            clearButton.addEventListener('click', () => {
                this.clearSearch();
            });
        }
    }
    
    /**
     * Handle search input with debouncing
     * @param {string} query - Search query
     */
    handleSearchInput(query) {
        // Clear previous timeout
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }
        
        // Reset selection
        this.selectedIndex = -1;
        
        // Handle empty query
        if (query.trim().length < 2) {
            this.hideResults();
            return;
        }
        
        // Debounce search
        this.searchTimeout = setTimeout(() => {
            this.performSearch(query.trim());
        }, 300);
    }
    
    /**
     * Handle keyboard navigation
     * @param {KeyboardEvent} e - Keyboard event
     */
    handleKeyboardNavigation(e) {
        const resultItems = this.searchResults.querySelectorAll('.search-result-item');
        
        if (resultItems.length === 0) return;
        
        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                this.selectedIndex = Math.min(this.selectedIndex + 1, resultItems.length - 1);
                this.highlightResult(resultItems, this.selectedIndex);
                break;
                
            case 'ArrowUp':
                e.preventDefault();
                this.selectedIndex = Math.max(this.selectedIndex - 1, -1);
                this.highlightResult(resultItems, this.selectedIndex);
                break;
                
            case 'Enter':
                e.preventDefault();
                if (this.selectedIndex >= 0 && resultItems[this.selectedIndex]) {
                    resultItems[this.selectedIndex].click();
                }
                break;
                
            case 'Escape':
                this.hideResults();
                this.searchInput.blur();
                break;
        }
    }
    
    /**
     * Perform search operation
     * @param {string} query - Search query
     */
    performSearch(query) {
        try {
            // Build or refresh search index
            const index = this.getSearchIndex();
            
            // Perform search
            const results = this.searchInIndex(query, index);
            
            // Display results
            this.displayResults(results, query);
            
        } catch (error) {
            console.error('Search failed:', error);
            this.showErrorMessage('搜索时出现错误，请重试');
        }
    }
    
    /**
     * Get or build search index
     * @returns {Array} Search index
     */
    getSearchIndex() {
        const now = Date.now();
        
        // Check if index needs rebuilding
        if (this.searchIndex.length === 0 || (now - this.lastBuildTime) > this.indexTTL) {
            this.buildSearchIndex();
        }
        
        return this.searchIndex;
    }
    
    /**
     * Build search index from historical events
     */
    buildSearchIndex() {
        console.log('Building search index...');
        
        // Check if data is available
        if (typeof window.allHistoricalEvents === 'undefined' || !window.allHistoricalEvents) {
            console.warn('Historical events data not available for search indexing');
            return;
        }
        
        this.searchIndex = [];
        
        window.allHistoricalEvents.forEach(event => {
            const searchableContent = this.extractSearchableContent(event);
            
            this.searchIndex.push({
                id: event.id,
                title: event.title,
                period: event.period,
                region: event.region || '',
                description: event.description || '',
                content: searchableContent.join(' ').toLowerCase(),
                originalEvent: event
            });
        });
        
        this.lastBuildTime = Date.now();
        console.log(`Search index built with ${this.searchIndex.length} entries`);
    }
    
    /**
     * Extract searchable content from event
     * @param {Object} event - Historical event
     * @returns {Array} Array of searchable strings
     */
    extractSearchableContent(event) {
        const content = [];
        
        // Basic fields
        if (event.title) content.push(event.title);
        if (event.period) content.push(event.period);
        if (event.region) content.push(event.region);
        if (event.description) content.push(event.description);
        
        // Analysis dimensions
        const analysisDimensions = ['philosophy', 'psychology', 'sociology', 'economics', 'aiInsights'];
        analysisDimensions.forEach(dim => {
            if (event[dim] && event[dim].content) {
                content.push(event[dim].content);
            }
            if (event[dim] && event[dim].questions) {
                content.push(...event[dim].questions);
            }
        });
        
        // Quiz content
        if (event.quiz && event.quiz.questions) {
            event.quiz.questions.forEach(q => {
                content.push(q.question);
                if (q.options) {
                    content.push(...q.options);
                }
            });
        }
        
        return content;
    }
    
    /**
     * Search within the index
     * @param {string} query - Search query
     * @param {Array} index - Search index
     * @returns {Array} Search results
     */
    searchInIndex(query, index) {
        const lowerQuery = query.toLowerCase();
        const results = [];
        
        index.forEach(item => {
            let score = 0;
            
            // Title match (highest priority)
            if (item.title.toLowerCase().includes(lowerQuery)) {
                score += 10;
            }
            
            // Period match
            if (item.period.toLowerCase().includes(lowerQuery)) {
                score += 5;
            }
            
            // Region match
            if (item.region.toLowerCase().includes(lowerQuery)) {
                score += 3;
            }
            
            // Content match
            if (item.content.includes(lowerQuery)) {
                score += 1;
            }
            
            if (score > 0) {
                results.push({
                    ...item.originalEvent,
                    searchScore: score
                });
            }
        });
        
        // Sort by relevance score
        return results.sort((a, b) => b.searchScore - a.searchScore);
    }
    
    /**
     * Display search results
     * @param {Array} results - Search results
     * @param {string} query - Original query
     */
    displayResults(results, query) {
        if (results.length === 0) {
            this.showNoResults();
            return;
        }
        
        const html = results.slice(0, 10).map(event => {
            return this.createResultHTML(event, query);
        }).join('');
        
        this.searchResults.innerHTML = html;
        this.searchResults.style.display = 'block';
        
        // Add click listeners
        this.addResultClickListeners();
    }
    
    /**
     * Create HTML for a single result
     * @param {Object} event - Event object
     * @param {string} query - Search query
     * @returns {string} HTML string
     */
    createResultHTML(event, query) {
        const snippet = this.createSnippet(event.description, query, 150);
        
        return `
            <div class="search-result-item" data-event-id="${event.id}">
                <h4 class="result-title">${this.highlightQuery(event.title, query)}</h4>
                <p class="result-period">时期: ${event.period}</p>
                ${event.region ? `<p class="result-region">地区: ${event.region}</p>` : ''}
                <p class="result-snippet">${snippet}</p>
                <div class="result-score" style="display: none;">相关度: ${event.searchScore}</div>
            </div>
        `;
    }
    
    /**
     * Create snippet with highlighted query
     * @param {string} text - Original text
     * @param {string} query - Search query
     * @param {number} maxLength - Maximum snippet length
     * @returns {string} Highlighted snippet
     */
    createSnippet(text, query, maxLength = 150) {
        if (!text) return '';
        
        const lowerText = text.toLowerCase();
        const lowerQuery = query.toLowerCase();
        const index = lowerText.indexOf(lowerQuery);
        
        if (index === -1) {
            return text.substring(0, maxLength) + (text.length > maxLength ? '...' : '');
        }
        
        // Calculate snippet boundaries
        const start = Math.max(0, index - 50);
        const end = Math.min(text.length, index + query.length + 50);
        
        let snippet = text.substring(start, end);
        
        // Add ellipsis
        if (start > 0) snippet = '...' + snippet;
        if (end < text.length) snippet = snippet + '...';
        
        // Highlight query
        return this.highlightQuery(snippet, query);
    }
    
    /**
     * Highlight query in text
     * @param {string} text - Text to highlight
     * @param {string} query - Query to highlight
     * @returns {string} Text with highlighted query
     */
    highlightQuery(text, query) {
        if (!text || !query) return text;
        
        const regex = new RegExp(`(${query})`, 'gi');
        return text.replace(regex, '<mark>$1</mark>');
    }
    
    /**
     * Add click listeners to result items
     */
    addResultClickListeners() {
        const resultItems = this.searchResults.querySelectorAll('.search-result-item');
        
        resultItems.forEach(item => {
            item.addEventListener('click', () => {
                const eventId = parseInt(item.dataset.eventId);
                this.handleResultClick(eventId);
            });
        });
    }
    
    /**
     * Handle result item click
     * @param {number} eventId - Event ID
     */
    handleResultClick(eventId) {
        // Hide search results
        this.hideResults();
        
        // Clear search input
        this.searchInput.value = '';
        
        // Dispatch event for other modules to handle
        const event = new CustomEvent('search:result-selected', {
            detail: { eventId }
        });
        window.dispatchEvent(event);
        
        // If event detail renderer is available, use it
        if (typeof window.renderEventDetail === 'function') {
            window.renderEventDetail(eventId);
        }
    }
    
    /**
     * Highlight selected result
     * @param {NodeList} items - Result items
     * @param {number} index - Selected index
     */
    highlightResult(items, index) {
        items.forEach((item, i) => {
            item.classList.toggle('selected', i === index);
        });
        
        // Scroll selected item into view
        if (index >= 0 && items[index]) {
            items[index].scrollIntoView({ block: 'nearest' });
        }
    }
    
    /**
     * Show no results message
     */
    showNoResults() {
        this.searchResults.innerHTML = `
            <div class="search-no-results">
                <p>未找到匹配的历史事件</p>
                <p class="search-suggestion">尝试使用不同的关键词或检查拼写</p>
            </div>
        `;
        this.searchResults.style.display = 'block';
    }
    
    /**
     * Show error message
     * @param {string} message - Error message
     */
    showErrorMessage(message) {
        this.searchResults.innerHTML = `
            <div class="search-error">
                <p>${message}</p>
            </div>
        `;
        this.searchResults.style.display = 'block';
    }
    
    /**
     * Hide search results
     */
    hideResults() {
        this.searchResults.style.display = 'none';
        this.selectedIndex = -1;
    }
    
    /**
     * Clear search
     */
    clearSearch() {
        this.searchInput.value = '';
        this.hideResults();
        this.searchInput.focus();
    }
    
    /**
     * Get search statistics
     * @returns {Object} Search statistics
     */
    getStats() {
        return {
            indexSize: this.searchIndex.length,
            lastBuildTime: this.lastBuildTime,
            isInitialized: this.isInitialized
        };
    }
    
    /**
     * Destroy search engine
     */
    destroy() {
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }
        
        this.searchIndex = [];
        this.isInitialized = false;
        
        console.log('Search engine destroyed');
    }
}

// Create singleton instance
const searchEngine = new SearchEngine();

// Export for module use
export default searchEngine;
