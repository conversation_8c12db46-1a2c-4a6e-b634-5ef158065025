/**
 * Timeline Module
 * Handles timeline visualization and navigation
 */

class Timeline {
    constructor() {
        this.timelineContainer = null;
        this.events = [];
        this.isInitialized = false;
        this.timelineWidth = 1000;
        this.minDistance = 80;
        this.selectedEventId = null;
    }

    /**
     * Initialize timeline
     */
    init() {
        if (this.isInitialized) return;

        // Get DOM elements
        this.timelineContainer = document.getElementById('timeline');

        if (!this.timelineContainer) {
            console.warn('Timeline container not found');
            return;
        }

        // Load events data
        this.loadEvents();

        // Create timeline
        this.createTimeline();

        // Set up event listeners
        this.setupEventListeners();

        this.isInitialized = true;
        console.log('Timeline initialized');
    }

    /**
     * Load events data
     */
    loadEvents() {
        if (typeof window.allHistoricalEvents !== 'undefined' && window.allHistoricalEvents) {
            this.events = [...window.allHistoricalEvents];
        } else {
            console.warn('Historical events data not available for timeline');
            this.events = [];
        }
    }

    /**
     * Create timeline visualization
     */
    createTimeline() {
        if (!this.timelineContainer || this.events.length === 0) {
            this.showEmptyTimeline();
            return;
        }

        // Clear existing content
        this.timelineContainer.innerHTML = '';

        // Sort events by time
        const sortedEvents = this.sortEventsByTime([...this.events]);

        // Calculate time range
        const { earliestYear, latestYear } = this.calculateTimeRange(sortedEvents);

        // Create timeline points
        this.createTimelinePoints(sortedEvents, earliestYear, latestYear);

        // Set container width
        this.timelineContainer.style.width = `${this.timelineWidth}px`;
    }

    /**
     * Sort events by chronological order
     * @param {Array} events - Events to sort
     * @returns {Array} Sorted events
     */
    sortEventsByTime(events) {
        return events.sort((a, b) => {
            const yearA = this.extractYear(a.period);
            const yearB = this.extractYear(b.period);

            const bcA = this.isBCE(a.period);
            const bcB = this.isBCE(b.period);

            // Handle BCE vs CE
            if (bcA && !bcB) return -1;
            if (!bcA && bcB) return 1;

            // Same era comparison
            return bcA ? yearB - yearA : yearA - yearB;
        });
    }

    /**
     * Calculate time range for timeline
     * @param {Array} sortedEvents - Chronologically sorted events
     * @returns {Object} Time range object
     */
    calculateTimeRange(sortedEvents) {
        if (sortedEvents.length === 0) {
            return { earliestYear: 0, latestYear: 2024 };
        }

        const earliestYear = this.extractYear(sortedEvents[0].period);
        const latestYear = this.extractYear(sortedEvents[sortedEvents.length - 1].period);

        return { earliestYear, latestYear };
    }

    /**
     * Create timeline points for events
     * @param {Array} sortedEvents - Sorted events
     * @param {number} earliestYear - Earliest year
     * @param {number} latestYear - Latest year
     */
    createTimelinePoints(sortedEvents, earliestYear, latestYear) {
        let lastX = -this.minDistance;

        sortedEvents.forEach(event => {
            const year = this.extractYear(event.period);
            const position = this.calculatePosition(year, earliestYear, latestYear);

            // Prevent overlap
            const finalPosition = Math.max(lastX + this.minDistance, position);
            lastX = finalPosition;

            // Create timeline point
            const point = this.createTimelinePoint(event, finalPosition);
            this.timelineContainer.appendChild(point);
        });
    }

    /**
     * Calculate position on timeline
     * @param {number} year - Event year
     * @param {number} earliestYear - Earliest year
     * @param {number} latestYear - Latest year
     * @returns {number} Position in pixels
     */
    calculatePosition(year, earliestYear, latestYear) {
        // Handle BCE/CE calculations
        const adjustedEarliestYear = earliestYear < 0 ? Math.abs(earliestYear) : 0;
        const adjustedYear = year < 0 ? Math.abs(year) + adjustedEarliestYear : adjustedEarliestYear + year;
        const adjustedTotalYears = earliestYear < 0 ? Math.abs(earliestYear) + latestYear : (latestYear - earliestYear);

        return (adjustedYear / adjustedTotalYears) * this.timelineWidth;
    }

    /**
     * Create a single timeline point
     * @param {Object} event - Event data
     * @param {number} position - Position in pixels
     * @returns {HTMLElement} Timeline point element
     */
    createTimelinePoint(event, position) {
        const point = document.createElement('div');
        point.className = 'timeline-point';
        point.setAttribute('data-event-id', event.id);
        point.setAttribute('role', 'button');
        point.setAttribute('tabindex', '0');
        point.setAttribute('aria-label', `查看事件: ${event.title}`);
        point.style.left = `${position}px`;

        // Create tooltip
        const tooltip = document.createElement('div');
        tooltip.className = 'timeline-tooltip';
        tooltip.innerHTML = `
            <div class="tooltip-title">${this.escapeHtml(event.title)}</div>
            <div class="tooltip-period">${this.escapeHtml(event.period)}</div>
        `;

        point.appendChild(tooltip);

        // Add event listeners
        this.addPointEventListeners(point, event);

        return point;
    }

    /**
     * Add event listeners to timeline point
     * @param {HTMLElement} point - Timeline point element
     * @param {Object} event - Event data
     */
    addPointEventListeners(point, event) {
        const selectHandler = () => {
            this.selectEvent(event.id);
        };

        // Click handler
        point.addEventListener('click', selectHandler);

        // Keyboard handler
        point.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                selectHandler();
            }
        });

        // Hover effects
        point.addEventListener('mouseenter', () => {
            this.showTooltip(point);
        });

        point.addEventListener('mouseleave', () => {
            this.hideTooltip(point);
        });
    }

    /**
     * Select an event on timeline
     * @param {number} eventId - Event ID
     */
    selectEvent(eventId) {
        // Update selected state
        this.selectedEventId = eventId;

        // Update visual state
        this.updateSelectedState();

        // Dispatch event for other modules
        this.dispatchEvent('timeline:event-selected', { eventId });

        // Scroll event into view if needed
        this.scrollToEvent(eventId);
    }

    /**
     * Update visual state of selected event
     */
    updateSelectedState() {
        const points = this.timelineContainer.querySelectorAll('.timeline-point');

        points.forEach(point => {
            const eventId = parseInt(point.getAttribute('data-event-id'));
            point.classList.toggle('selected', eventId === this.selectedEventId);
        });
    }

    /**
     * Scroll to specific event
     * @param {number} eventId - Event ID
     */
    scrollToEvent(eventId) {
        const point = this.timelineContainer.querySelector(`[data-event-id="${eventId}"]`);
        if (point) {
            point.scrollIntoView({
                behavior: 'smooth',
                block: 'nearest',
                inline: 'center'
            });
        }
    }

    /**
     * Show tooltip for timeline point
     * @param {HTMLElement} point - Timeline point
     */
    showTooltip(point) {
        const tooltip = point.querySelector('.timeline-tooltip');
        if (tooltip) {
            tooltip.style.display = 'block';
        }
    }

    /**
     * Hide tooltip for timeline point
     * @param {HTMLElement} point - Timeline point
     */
    hideTooltip(point) {
        const tooltip = point.querySelector('.timeline-tooltip');
        if (tooltip) {
            tooltip.style.display = 'none';
        }
    }

    /**
     * Set up global event listeners
     */
    setupEventListeners() {
        // Listen for window resize
        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // Listen for event selections from other modules
        window.addEventListener('events:rendered', () => {
            this.loadEvents();
            this.createTimeline();
        });
    }

    /**
     * Handle window resize
     */
    handleResize() {
        // Debounce resize handling
        if (this.resizeTimeout) {
            clearTimeout(this.resizeTimeout);
        }

        this.resizeTimeout = setTimeout(() => {
            this.createTimeline();
        }, 250);
    }

    /**
     * Show empty timeline message
     */
    showEmptyTimeline() {
        if (this.timelineContainer) {
            this.timelineContainer.innerHTML = `
                <div class="timeline-empty">
                    <p>暂无历史事件数据</p>
                </div>
            `;
        }
    }

    /**
     * Extract year from period string
     * @param {string} period - Period string
     * @returns {number} Extracted year
     */
    extractYear(period) {
        if (!period) return 0;

        // Handle BCE/CE patterns
        const bcMatch = period.match(/(公元前|BC)?\s*(\d+)(万年|年前|年)/i);
        if (bcMatch) {
            const year = parseInt(bcMatch[2]);
            return bcMatch[1] ? -year : year;
        }

        // Handle specific years
        const yearMatch = period.match(/\d+/);
        if (yearMatch) {
            return parseInt(yearMatch[0]);
        }

        return 0;
    }

    /**
     * Check if period is BCE
     * @param {string} period - Period string
     * @returns {boolean} Whether period is BCE
     */
    isBCE(period) {
        return period.includes('公元前') || period.includes('BC');
    }

    /**
     * Escape HTML to prevent XSS
     * @param {string} text - Text to escape
     * @returns {string} Escaped text
     */
    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Dispatch custom event
     * @param {string} eventType - Event type
     * @param {Object} detail - Event detail
     */
    dispatchEvent(eventType, detail = {}) {
        const event = new CustomEvent(eventType, { detail });
        window.dispatchEvent(event);
    }

    /**
     * Get timeline statistics
     * @returns {Object} Timeline statistics
     */
    getStats() {
        return {
            eventCount: this.events.length,
            selectedEventId: this.selectedEventId,
            isInitialized: this.isInitialized
        };
    }

    /**
     * Destroy timeline
     */
    destroy() {
        if (this.resizeTimeout) {
            clearTimeout(this.resizeTimeout);
        }

        this.events = [];
        this.selectedEventId = null;
        this.isInitialized = false;

        console.log('Timeline destroyed');
    }
}

// Create singleton instance
const timeline = new Timeline();

// Export for module use
export default timeline;