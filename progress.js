// 初始化进度条
function setupProgressBar() {
    const progressBar = document.getElementById('progress-bar');
    if (!progressBar) return;
    
    // 监听事件列表渲染完成
    document.addEventListener('eventsRendered', () => {
        updateProgressBar();
    });
    
    // 监听事件详情显示
    document.addEventListener('eventDetailRendered', () => {
        updateProgressBar();
    });
}

// 更新进度条
function updateProgressBar() {
    const progressBar = document.getElementById('progress-bar');
    if (!progressBar) return;
    
    // 获取统计信息
    const totalEvents = allHistoricalEvents ? allHistoricalEvents.length : 0;
    const readEvents = getReadEvents().length;
    
    // 获取测验结果（使用更健壮的解析方式）
    let quizCompleted = 0;
    try {
        const quizResults = getQuizResults();
        quizCompleted = Object.keys(quizResults || {}).length;
    } catch (error) {
        console.error('Failed to get quiz results:', error);
    }
    
    // 计算进度（阅读事件和完成测验各占50%）
    const progress = totalEvents > 0 ? 
        Math.min(100, ((readEvents / totalEvents) + (quizCompleted / totalEvents)) * 50) : 0;
    
    // 更新进度条样式
    progressBar.style.width = `${progress.toFixed(2)}%`;
    progressBar.setAttribute('aria-valuenow', progress.toFixed(0));
    progressBar.textContent = `${progress.toFixed(0)}% 完成`;
    
    // 根据进度更新颜色
    if (progress < 30) {
        progressBar.classList.remove('medium', 'high');
    } else if (progress < 70) {
        progressBar.classList.add('medium');
        progressBar.classList.remove('high');
    } else {
        progressBar.classList.add('high');
        progressBar.classList.remove('medium');
    }
}


// 显示错误通知
function showErrorNotification(message) {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = 'error-notification';
    notification.innerHTML = `
        <span>⚠</span> ${message}
        <button class="close-btn">&times;</button>
    `;
    
    // 添加到页面
    document.body.appendChild(notification);
    
    // 添加关闭功能
    const closeBtn = notification.querySelector('.close-btn');
    closeBtn.addEventListener('click', () => {
        notification.remove();
    });
    
    // 5秒后自动关闭
    setTimeout(() => {
        notification.remove();
    }, 5000);
}
