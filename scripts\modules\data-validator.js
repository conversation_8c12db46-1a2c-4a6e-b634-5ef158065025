/**
 * Data Validator Module
 * Provides comprehensive data validation for historical events and user inputs
 */

class DataValidator {
    constructor() {
        this.validationRules = {
            event: {
                id: { type: 'number', required: true, min: 1 },
                title: { type: 'string', required: true, minLength: 1, maxLength: 200 },
                period: { type: 'string', required: true, minLength: 1, maxLength: 100 },
                description: { type: 'string', required: true, minLength: 10, maxLength: 5000 },
                region: { type: 'string', required: false, maxLength: 100 }
            },
            quiz: {
                question: { type: 'string', required: true, minLength: 5, maxLength: 500 },
                options: { type: 'array', required: true, minLength: 2, maxLength: 6 },
                answer: { type: 'number', required: true, min: 0 }
            },
            analysis: {
                content: { type: 'string', required: true, minLength: 10, maxLength: 2000 },
                questions: { type: 'array', required: false, maxLength: 10 }
            }
        };
        
        this.errorMessages = {
            required: '此字段为必填项',
            type: '数据类型不正确',
            minLength: '内容长度不足',
            maxLength: '内容长度超出限制',
            min: '数值过小',
            max: '数值过大',
            pattern: '格式不正确',
            custom: '验证失败'
        };
    }
    
    /**
     * Validate historical event data
     * @param {Object} event - Event data to validate
     * @returns {Object} Validation result
     */
    validateEvent(event) {
        const result = {
            isValid: true,
            errors: [],
            warnings: []
        };
        
        if (!event || typeof event !== 'object') {
            result.isValid = false;
            result.errors.push('事件数据必须是一个对象');
            return result;
        }
        
        // Validate basic event fields
        this.validateFields(event, this.validationRules.event, result);
        
        // Validate analysis sections
        const analysisDimensions = ['philosophy', 'psychology', 'sociology', 'economics'];
        analysisDimensions.forEach(dimension => {
            if (event[dimension]) {
                const analysisResult = this.validateAnalysis(event[dimension], dimension);
                if (!analysisResult.isValid) {
                    result.isValid = false;
                    result.errors.push(...analysisResult.errors.map(err => `${dimension}: ${err}`));
                }
                result.warnings.push(...analysisResult.warnings.map(warn => `${dimension}: ${warn}`));
            }
        });
        
        // Validate quiz if present
        if (event.quiz) {
            const quizResult = this.validateQuiz(event.quiz);
            if (!quizResult.isValid) {
                result.isValid = false;
                result.errors.push(...quizResult.errors.map(err => `quiz: ${err}`));
            }
            result.warnings.push(...quizResult.warnings.map(warn => `quiz: ${warn}`));
        }
        
        // Additional semantic validations
        this.performSemanticValidation(event, result);
        
        return result;
    }
    
    /**
     * Validate analysis section
     * @param {Object} analysis - Analysis data
     * @param {string} dimension - Analysis dimension name
     * @returns {Object} Validation result
     */
    validateAnalysis(analysis, dimension) {
        const result = {
            isValid: true,
            errors: [],
            warnings: []
        };
        
        if (!analysis || typeof analysis !== 'object') {
            result.isValid = false;
            result.errors.push('分析数据必须是一个对象');
            return result;
        }
        
        this.validateFields(analysis, this.validationRules.analysis, result);
        
        // Validate questions array if present
        if (analysis.questions && Array.isArray(analysis.questions)) {
            analysis.questions.forEach((question, index) => {
                if (typeof question !== 'string' || question.trim().length < 5) {
                    result.warnings.push(`问题 ${index + 1} 内容过短或格式不正确`);
                }
            });
        }
        
        return result;
    }
    
    /**
     * Validate quiz data
     * @param {Object} quiz - Quiz data
     * @returns {Object} Validation result
     */
    validateQuiz(quiz) {
        const result = {
            isValid: true,
            errors: [],
            warnings: []
        };
        
        if (!quiz || typeof quiz !== 'object') {
            result.isValid = false;
            result.errors.push('测验数据必须是一个对象');
            return result;
        }
        
        // Validate quiz questions array
        if (!quiz.questions || !Array.isArray(quiz.questions)) {
            result.isValid = false;
            result.errors.push('测验必须包含问题数组');
            return result;
        }
        
        quiz.questions.forEach((question, index) => {
            const questionResult = this.validateQuizQuestion(question, index);
            if (!questionResult.isValid) {
                result.isValid = false;
                result.errors.push(...questionResult.errors);
            }
            result.warnings.push(...questionResult.warnings);
        });
        
        return result;
    }
    
    /**
     * Validate individual quiz question
     * @param {Object} question - Quiz question data
     * @param {number} index - Question index
     * @returns {Object} Validation result
     */
    validateQuizQuestion(question, index) {
        const result = {
            isValid: true,
            errors: [],
            warnings: []
        };
        
        const prefix = `问题 ${index + 1}`;
        
        if (!question || typeof question !== 'object') {
            result.isValid = false;
            result.errors.push(`${prefix}: 必须是一个对象`);
            return result;
        }
        
        // Validate question fields
        this.validateFields(question, this.validationRules.quiz, result, prefix);
        
        // Validate options array
        if (question.options && Array.isArray(question.options)) {
            if (question.options.length < 2) {
                result.isValid = false;
                result.errors.push(`${prefix}: 至少需要2个选项`);
            }
            
            question.options.forEach((option, optIndex) => {
                if (typeof option !== 'string' || option.trim().length === 0) {
                    result.warnings.push(`${prefix}: 选项 ${optIndex + 1} 为空或格式不正确`);
                }
            });
            
            // Validate answer index
            if (typeof question.answer === 'number') {
                if (question.answer < 0 || question.answer >= question.options.length) {
                    result.isValid = false;
                    result.errors.push(`${prefix}: 答案索引超出选项范围`);
                }
            }
        }
        
        return result;
    }
    
    /**
     * Validate fields against rules
     * @param {Object} data - Data to validate
     * @param {Object} rules - Validation rules
     * @param {Object} result - Result object to update
     * @param {string} prefix - Error message prefix
     */
    validateFields(data, rules, result, prefix = '') {
        Object.entries(rules).forEach(([field, rule]) => {
            const value = data[field];
            const fieldName = prefix ? `${prefix}: ${field}` : field;
            
            // Check required fields
            if (rule.required && (value === undefined || value === null || value === '')) {
                result.isValid = false;
                result.errors.push(`${fieldName} ${this.errorMessages.required}`);
                return;
            }
            
            // Skip validation if field is not required and empty
            if (!rule.required && (value === undefined || value === null || value === '')) {
                return;
            }
            
            // Type validation
            if (!this.validateType(value, rule.type)) {
                result.isValid = false;
                result.errors.push(`${fieldName} ${this.errorMessages.type}`);
                return;
            }
            
            // Length/size validation
            if (rule.minLength && this.getLength(value) < rule.minLength) {
                result.isValid = false;
                result.errors.push(`${fieldName} ${this.errorMessages.minLength} (最少${rule.minLength}个字符)`);
            }
            
            if (rule.maxLength && this.getLength(value) > rule.maxLength) {
                result.isValid = false;
                result.errors.push(`${fieldName} ${this.errorMessages.maxLength} (最多${rule.maxLength}个字符)`);
            }
            
            // Numeric range validation
            if (rule.min !== undefined && value < rule.min) {
                result.isValid = false;
                result.errors.push(`${fieldName} ${this.errorMessages.min} (最小值: ${rule.min})`);
            }
            
            if (rule.max !== undefined && value > rule.max) {
                result.isValid = false;
                result.errors.push(`${fieldName} ${this.errorMessages.max} (最大值: ${rule.max})`);
            }
            
            // Pattern validation
            if (rule.pattern && !rule.pattern.test(value)) {
                result.isValid = false;
                result.errors.push(`${fieldName} ${this.errorMessages.pattern}`);
            }
        });
    }
    
    /**
     * Perform semantic validation
     * @param {Object} event - Event data
     * @param {Object} result - Result object to update
     */
    performSemanticValidation(event, result) {
        // Check for reasonable period format
        if (event.period && !this.isValidPeriod(event.period)) {
            result.warnings.push('时期格式可能不正确，建议使用标准格式');
        }
        
        // Check for consistent analysis content
        const analysisDimensions = ['philosophy', 'psychology', 'sociology', 'economics'];
        const hasAnalysis = analysisDimensions.some(dim => event[dim] && event[dim].content);
        
        if (!hasAnalysis) {
            result.warnings.push('建议添加至少一个维度的分析内容');
        }
        
        // Check for quiz presence
        if (!event.quiz || !event.quiz.questions || event.quiz.questions.length === 0) {
            result.warnings.push('建议添加测验问题以增强学习效果');
        }
    }
    
    /**
     * Validate data type
     * @param {*} value - Value to validate
     * @param {string} expectedType - Expected type
     * @returns {boolean} Whether type is valid
     */
    validateType(value, expectedType) {
        switch (expectedType) {
            case 'string':
                return typeof value === 'string';
            case 'number':
                return typeof value === 'number' && !isNaN(value);
            case 'boolean':
                return typeof value === 'boolean';
            case 'array':
                return Array.isArray(value);
            case 'object':
                return typeof value === 'object' && value !== null && !Array.isArray(value);
            default:
                return true;
        }
    }
    
    /**
     * Get length of value
     * @param {*} value - Value to measure
     * @returns {number} Length
     */
    getLength(value) {
        if (typeof value === 'string') {
            return value.length;
        }
        if (Array.isArray(value)) {
            return value.length;
        }
        return 0;
    }
    
    /**
     * Check if period format is valid
     * @param {string} period - Period string
     * @returns {boolean} Whether period is valid
     */
    isValidPeriod(period) {
        const patterns = [
            /^\d+年$/,                          // 2024年
            /^公元前\d+年$/,                    // 公元前500年
            /^\d+年前$/,                        // 70000年前
            /^\d+-\d+年$/,                      // 1760-1840年
            /^约\d+年前$/,                      // 约70000年前
            /^\d+世纪$/,                        // 18世纪
            /^公元前\d+世纪$/                   // 公元前5世纪
        ];
        
        return patterns.some(pattern => pattern.test(period));
    }
    
    /**
     * Validate user input
     * @param {string} input - User input
     * @param {string} type - Input type
     * @returns {Object} Validation result
     */
    validateUserInput(input, type) {
        const result = {
            isValid: true,
            errors: [],
            sanitized: input
        };
        
        switch (type) {
            case 'search':
                result.sanitized = this.sanitizeSearchInput(input);
                if (result.sanitized.length > 100) {
                    result.isValid = false;
                    result.errors.push('搜索关键词过长');
                }
                break;
                
            case 'comment':
                result.sanitized = this.sanitizeTextInput(input);
                if (result.sanitized.length > 1000) {
                    result.isValid = false;
                    result.errors.push('评论内容过长');
                }
                break;
                
            default:
                result.sanitized = this.sanitizeTextInput(input);
        }
        
        return result;
    }
    
    /**
     * Sanitize search input
     * @param {string} input - Search input
     * @returns {string} Sanitized input
     */
    sanitizeSearchInput(input) {
        if (typeof input !== 'string') return '';
        
        return input
            .trim()
            .replace(/[<>]/g, '') // Remove potential HTML
            .substring(0, 100);   // Limit length
    }
    
    /**
     * Sanitize text input
     * @param {string} input - Text input
     * @returns {string} Sanitized input
     */
    sanitizeTextInput(input) {
        if (typeof input !== 'string') return '';
        
        return input
            .trim()
            .replace(/[<>]/g, '') // Remove potential HTML
            .replace(/\s+/g, ' '); // Normalize whitespace
    }
    
    /**
     * Validate event collection
     * @param {Array} events - Array of events
     * @returns {Object} Validation result
     */
    validateEventCollection(events) {
        const result = {
            isValid: true,
            errors: [],
            warnings: [],
            validEvents: [],
            invalidEvents: []
        };
        
        if (!Array.isArray(events)) {
            result.isValid = false;
            result.errors.push('事件数据必须是一个数组');
            return result;
        }
        
        const seenIds = new Set();
        
        events.forEach((event, index) => {
            const eventResult = this.validateEvent(event);
            
            if (eventResult.isValid) {
                // Check for duplicate IDs
                if (seenIds.has(event.id)) {
                    result.warnings.push(`事件 ${index + 1}: ID ${event.id} 重复`);
                } else {
                    seenIds.add(event.id);
                }
                
                result.validEvents.push(event);
            } else {
                result.isValid = false;
                result.invalidEvents.push({ index, event, errors: eventResult.errors });
                result.errors.push(`事件 ${index + 1}: ${eventResult.errors.join(', ')}`);
            }
            
            result.warnings.push(...eventResult.warnings.map(warn => `事件 ${index + 1}: ${warn}`));
        });
        
        return result;
    }
}

// Create singleton instance
const dataValidator = new DataValidator();

// Export for module use
export default dataValidator;
