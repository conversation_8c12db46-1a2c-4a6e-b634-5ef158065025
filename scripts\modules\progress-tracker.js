/**
 * Progress Tracker Module
 * Tracks user learning progress and quiz results
 */

class ProgressTracker {
    constructor() {
        this.progressDisplay = null;
        this.progressBar = null;
        this.completedCount = null;
        this.totalCount = null;
        this.isInitialized = false;
        
        // Storage keys
        this.STORAGE_KEYS = {
            READ_EVENTS: 'readEvents',
            QUIZ_RESULTS: 'quizResults',
            LEARNING_PROGRESS: 'learningProgress'
        };
    }
    
    /**
     * Initialize progress tracker
     */
    init() {
        if (this.isInitialized) return;
        
        // Get DOM elements
        this.progressDisplay = document.getElementById('progress-display');
        this.progressBar = document.querySelector('.progress-bar');
        this.completedCount = document.getElementById('completed-count');
        this.totalCount = document.getElementById('total-count');
        
        if (!this.progressDisplay) {
            console.warn('Progress display elements not found');
            return;
        }
        
        // Set up event listeners
        this.setupEventListeners();
        
        // Update initial display
        this.updateProgressDisplay();
        
        this.isInitialized = true;
        console.log('Progress tracker initialized');
    }
    
    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Listen for event detail views
        window.addEventListener('event:detail-rendered', (e) => {
            this.markEventAsRead(e.detail.eventId);
        });
        
        // Listen for quiz completions
        window.addEventListener('quiz:completed', (e) => {
            this.recordQuizResult(e.detail.eventId, e.detail.score);
        });
        
        // Listen for events rendered
        window.addEventListener('events:rendered', () => {
            this.updateProgressDisplay();
        });
    }
    
    /**
     * Mark an event as read
     * @param {number} eventId - Event ID
     */
    markEventAsRead(eventId) {
        const readEvents = this.getReadEvents();

        if (!readEvents.includes(eventId)) {
            readEvents.push(eventId);
            this.saveReadEvents(readEvents);
            this.addReadingLogEntry(eventId);
            this.updateProgressDisplay();

            // Check for new achievements
            const stats = this.calculateProgressStats();
            this.calculateAchievements(stats);

            // Dispatch progress update event
            this.dispatchProgressEvent('progress:event-read', { eventId });
        }
    }
    
    /**
     * Mark an event as unread
     * @param {number} eventId - Event ID
     */
    markEventAsUnread(eventId) {
        const readEvents = this.getReadEvents();
        const index = readEvents.indexOf(eventId);
        
        if (index > -1) {
            readEvents.splice(index, 1);
            this.saveReadEvents(readEvents);
            this.updateProgressDisplay();
            
            // Dispatch progress update event
            this.dispatchProgressEvent('progress:event-unread', { eventId });
        }
    }
    
    /**
     * Record quiz result
     * @param {number} eventId - Event ID
     * @param {number} score - Quiz score
     */
    recordQuizResult(eventId, score) {
        const quizResults = this.getQuizResults();
        const timestamp = new Date().toISOString();
        
        // Update or create quiz result
        if (quizResults[eventId]) {
            quizResults[eventId].score = Math.max(quizResults[eventId].score, score);
            quizResults[eventId].attempts += 1;
            quizResults[eventId].lastAttempt = timestamp;
        } else {
            quizResults[eventId] = {
                score: score,
                attempts: 1,
                firstAttempt: timestamp,
                lastAttempt: timestamp
            };
        }
        
        this.saveQuizResults(quizResults);
        this.updateProgressDisplay();
        
        // Automatically mark as read if quiz completed
        this.markEventAsRead(eventId);
        
        // Dispatch quiz result event
        this.dispatchProgressEvent('progress:quiz-completed', { 
            eventId, 
            score, 
            result: quizResults[eventId] 
        });
    }
    
    /**
     * Get read events from storage
     * @returns {Array} Array of read event IDs
     */
    getReadEvents() {
        try {
            const stored = localStorage.getItem(this.STORAGE_KEYS.READ_EVENTS);
            return stored ? JSON.parse(stored) : [];
        } catch (error) {
            console.error('Failed to load read events:', error);
            return [];
        }
    }
    
    /**
     * Save read events to storage
     * @param {Array} readEvents - Array of read event IDs
     */
    saveReadEvents(readEvents) {
        try {
            localStorage.setItem(this.STORAGE_KEYS.READ_EVENTS, JSON.stringify(readEvents));
        } catch (error) {
            console.error('Failed to save read events:', error);
        }
    }
    
    /**
     * Get quiz results from storage
     * @returns {Object} Quiz results object
     */
    getQuizResults() {
        try {
            const stored = localStorage.getItem(this.STORAGE_KEYS.QUIZ_RESULTS);
            return stored ? JSON.parse(stored) : {};
        } catch (error) {
            console.error('Failed to load quiz results:', error);
            return {};
        }
    }
    
    /**
     * Save quiz results to storage
     * @param {Object} quizResults - Quiz results object
     */
    saveQuizResults(quizResults) {
        try {
            localStorage.setItem(this.STORAGE_KEYS.QUIZ_RESULTS, JSON.stringify(quizResults));
        } catch (error) {
            console.error('Failed to save quiz results:', error);
        }
    }
    
    /**
     * Update progress display
     */
    updateProgressDisplay() {
        const stats = this.calculateProgressStats();
        
        // Update progress bar
        if (this.progressBar) {
            const progressFill = this.progressBar.querySelector('.progress-fill');
            if (progressFill) {
                progressFill.style.width = `${stats.readPercentage}%`;
            }
            
            this.progressBar.setAttribute('aria-valuenow', stats.readPercentage);
        }
        
        // Update counters
        if (this.completedCount) {
            this.completedCount.textContent = stats.readCount;
        }
        
        if (this.totalCount) {
            this.totalCount.textContent = stats.totalCount;
        }
        
        // Update progress text
        const progressText = this.progressDisplay?.querySelector('.progress-text');
        if (progressText) {
            progressText.innerHTML = `
                已完成 <span class="completed">${stats.readCount}</span> / 
                <span class="total">${stats.totalCount}</span> 个事件
                ${stats.quizCount > 0 ? `<br>完成测验: ${stats.quizCount} 个` : ''}
            `;
        }
    }
    
    /**
     * Calculate progress statistics
     * @returns {Object} Progress statistics
     */
    calculateProgressStats() {
        const readEvents = this.getReadEvents();
        const quizResults = this.getQuizResults();
        
        // Get total events count
        const totalCount = typeof window.allHistoricalEvents !== 'undefined' 
            ? window.allHistoricalEvents.length 
            : 0;
        
        const readCount = readEvents.length;
        const readPercentage = totalCount > 0 ? Math.round((readCount / totalCount) * 100) : 0;
        const quizCount = Object.keys(quizResults).length;
        
        // Calculate average quiz score
        const quizScores = Object.values(quizResults).map(result => result.score);
        const averageScore = quizScores.length > 0 
            ? Math.round(quizScores.reduce((sum, score) => sum + score, 0) / quizScores.length)
            : 0;
        
        return {
            totalCount,
            readCount,
            readPercentage,
            quizCount,
            averageScore,
            readEvents,
            quizResults
        };
    }
    
    /**
     * Get progress for specific event
     * @param {number} eventId - Event ID
     * @returns {Object} Event progress data
     */
    getEventProgress(eventId) {
        const readEvents = this.getReadEvents();
        const quizResults = this.getQuizResults();
        
        return {
            isRead: readEvents.includes(eventId),
            hasQuiz: eventId in quizResults,
            quizResult: quizResults[eventId] || null
        };
    }
    
    /**
     * Get overall progress summary
     * @returns {Object} Progress summary
     */
    getProgressSummary() {
        const stats = this.calculateProgressStats();
        
        return {
            completion: {
                percentage: stats.readPercentage,
                completed: stats.readCount,
                total: stats.totalCount
            },
            quizzes: {
                completed: stats.quizCount,
                averageScore: stats.averageScore
            },
            achievements: this.calculateAchievements(stats)
        };
    }
    
    /**
     * Calculate achievements based on progress
     * @param {Object} stats - Progress statistics
     * @returns {Array} Array of achievements
     */
    calculateAchievements(stats) {
        const achievements = [];
        const unlockedAchievements = this.getUnlockedAchievements();

        // Define all possible achievements
        const allAchievements = [
            // Reading achievements
            {
                id: 'first_read',
                name: '📚 初次阅读',
                description: '完成第一个历史事件的学习',
                condition: () => stats.readCount >= 1,
                rarity: 'common',
                points: 10
            },
            {
                id: 'early_explorer',
                name: '🔍 早期探索者',
                description: '阅读3个不同的历史事件',
                condition: () => stats.readCount >= 3,
                rarity: 'common',
                points: 20
            },
            {
                id: 'dedicated_learner',
                name: '📖 专注学习者',
                description: '阅读5个历史事件',
                condition: () => stats.readCount >= 5,
                rarity: 'uncommon',
                points: 50
            },
            {
                id: 'half_complete',
                name: '🎯 学习过半',
                description: '完成50%的历史事件学习',
                condition: () => stats.readPercentage >= 50,
                rarity: 'rare',
                points: 100
            },
            {
                id: 'completionist',
                name: '🏆 完美主义者',
                description: '完成所有历史事件的学习',
                condition: () => stats.readPercentage >= 100,
                rarity: 'legendary',
                points: 500
            },

            // Quiz achievements
            {
                id: 'first_quiz',
                name: '🧠 初次测验',
                description: '完成第一个测验',
                condition: () => stats.quizCount >= 1,
                rarity: 'common',
                points: 15
            },
            {
                id: 'quiz_enthusiast',
                name: '🎲 测验爱好者',
                description: '完成3个测验',
                condition: () => stats.quizCount >= 3,
                rarity: 'uncommon',
                points: 30
            },
            {
                id: 'quiz_master',
                name: '🎓 测验大师',
                description: '平均测验分数达到80分以上',
                condition: () => stats.averageScore >= 80 && stats.quizCount >= 3,
                rarity: 'rare',
                points: 150
            },
            {
                id: 'perfect_score',
                name: '⭐ 完美分数',
                description: '在任意测验中获得100分',
                condition: () => this.hasPerf ectScore(),
                rarity: 'epic',
                points: 200
            },
            {
                id: 'quiz_champion',
                name: '👑 测验冠军',
                description: '所有测验平均分达到90分以上',
                condition: () => stats.averageScore >= 90 && stats.quizCount >= 5,
                rarity: 'legendary',
                points: 300
            },

            // Time-based achievements
            {
                id: 'speed_reader',
                name: '⚡ 速读者',
                description: '在一天内阅读3个事件',
                condition: () => this.checkDailyReading(3),
                rarity: 'uncommon',
                points: 40
            },
            {
                id: 'marathon_learner',
                name: '🏃 马拉松学习者',
                description: '连续7天进行学习',
                condition: () => this.checkConsecutiveDays(7),
                rarity: 'rare',
                points: 120
            },

            // Special achievements
            {
                id: 'cognitive_revolution_expert',
                name: '🧬 认知革命专家',
                description: '深入学习认知革命相关内容',
                condition: () => this.checkEventExpertise(1),
                rarity: 'epic',
                points: 100
            },
            {
                id: 'history_scholar',
                name: '📜 历史学者',
                description: '在所有维度分析中都有深入思考',
                condition: () => this.checkAnalysisEngagement(),
                rarity: 'legendary',
                points: 400
            }
        ];

        // Check which achievements are newly unlocked
        allAchievements.forEach(achievement => {
            if (achievement.condition() && !unlockedAchievements.includes(achievement.id)) {
                achievements.push(achievement);
                this.unlockAchievement(achievement);
            }
        });

        return achievements;
    }

    /**
     * Check if user has achieved a perfect score
     * @returns {boolean} Whether user has perfect score
     */
    hasPerfectScore() {
        const quizResults = this.getQuizResults();
        return Object.values(quizResults).some(result => {
            const percentage = (result.score / result.totalQuestions) * 100;
            return percentage === 100;
        });
    }

    /**
     * Check daily reading activity
     * @param {number} targetCount - Target reading count
     * @returns {boolean} Whether target is met
     */
    checkDailyReading(targetCount) {
        const today = new Date().toDateString();
        const readingLog = this.getReadingLog();
        const todayReading = readingLog.filter(entry =>
            new Date(entry.timestamp).toDateString() === today
        );
        return todayReading.length >= targetCount;
    }

    /**
     * Check consecutive learning days
     * @param {number} targetDays - Target consecutive days
     * @returns {boolean} Whether target is met
     */
    checkConsecutiveDays(targetDays) {
        const readingLog = this.getReadingLog();
        if (readingLog.length === 0) return false;

        const dates = [...new Set(readingLog.map(entry =>
            new Date(entry.timestamp).toDateString()
        ))].sort((a, b) => new Date(b) - new Date(a));

        let consecutiveDays = 1;
        for (let i = 1; i < dates.length; i++) {
            const currentDate = new Date(dates[i]);
            const previousDate = new Date(dates[i - 1]);
            const dayDiff = (previousDate - currentDate) / (1000 * 60 * 60 * 24);

            if (dayDiff === 1) {
                consecutiveDays++;
            } else {
                break;
            }
        }

        return consecutiveDays >= targetDays;
    }

    /**
     * Check expertise in specific event
     * @param {number} eventId - Event ID
     * @returns {boolean} Whether user is expert
     */
    checkEventExpertise(eventId) {
        const readEvents = this.getReadEvents();
        const quizResults = this.getQuizResults();

        const hasRead = readEvents.includes(eventId);
        const hasQuiz = quizResults[eventId];
        const hasGoodScore = hasQuiz && (hasQuiz.score / hasQuiz.totalQuestions) >= 0.8;

        return hasRead && hasGoodScore;
    }

    /**
     * Check analysis engagement
     * @returns {boolean} Whether user engages with analysis
     */
    checkAnalysisEngagement() {
        // This would require tracking analysis interactions
        // For now, return based on reading completion
        const stats = this.calculateProgressStats();
        return stats.readPercentage >= 80 && stats.averageScore >= 85;
    }

    /**
     * Get unlocked achievements
     * @returns {Array} Array of unlocked achievement IDs
     */
    getUnlockedAchievements() {
        try {
            const stored = localStorage.getItem('unlockedAchievements');
            return stored ? JSON.parse(stored) : [];
        } catch (error) {
            console.error('Failed to load achievements:', error);
            return [];
        }
    }

    /**
     * Unlock an achievement
     * @param {Object} achievement - Achievement object
     */
    unlockAchievement(achievement) {
        const unlocked = this.getUnlockedAchievements();
        if (!unlocked.includes(achievement.id)) {
            unlocked.push(achievement.id);
            this.saveUnlockedAchievements(unlocked);

            // Show achievement notification
            this.showAchievementNotification(achievement);

            // Dispatch achievement event
            this.dispatchProgressEvent('achievement:unlocked', {
                achievement: achievement,
                timestamp: Date.now()
            });
        }
    }

    /**
     * Save unlocked achievements
     * @param {Array} achievements - Array of achievement IDs
     */
    saveUnlockedAchievements(achievements) {
        try {
            localStorage.setItem('unlockedAchievements', JSON.stringify(achievements));
        } catch (error) {
            console.error('Failed to save achievements:', error);
        }
    }

    /**
     * Show achievement notification
     * @param {Object} achievement - Achievement object
     */
    showAchievementNotification(achievement) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `achievement-notification ${achievement.rarity}`;
        notification.innerHTML = `
            <div class="achievement-content">
                <div class="achievement-icon">🏆</div>
                <div class="achievement-info">
                    <h4>成就解锁！</h4>
                    <p class="achievement-name">${achievement.name}</p>
                    <p class="achievement-description">${achievement.description}</p>
                    <p class="achievement-points">+${achievement.points} 积分</p>
                </div>
            </div>
        `;

        // Add to page
        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => notification.classList.add('show'), 100);

        // Remove after delay
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 4000);
    }

    /**
     * Get reading log
     * @returns {Array} Reading log entries
     */
    getReadingLog() {
        try {
            const stored = localStorage.getItem('readingLog');
            return stored ? JSON.parse(stored) : [];
        } catch (error) {
            console.error('Failed to load reading log:', error);
            return [];
        }
    }

    /**
     * Add reading log entry
     * @param {number} eventId - Event ID
     */
    addReadingLogEntry(eventId) {
        const log = this.getReadingLog();
        log.push({
            eventId: eventId,
            timestamp: Date.now()
        });

        try {
            localStorage.setItem('readingLog', JSON.stringify(log));
        } catch (error) {
            console.error('Failed to save reading log:', error);
        }
    }
    
    /**
     * Export progress data
     * @returns {Object} Exportable progress data
     */
    exportProgress() {
        return {
            readEvents: this.getReadEvents(),
            quizResults: this.getQuizResults(),
            exportDate: new Date().toISOString(),
            version: '1.0'
        };
    }
    
    /**
     * Import progress data
     * @param {Object} progressData - Progress data to import
     * @returns {boolean} Success status
     */
    importProgress(progressData) {
        try {
            if (progressData.readEvents) {
                this.saveReadEvents(progressData.readEvents);
            }
            
            if (progressData.quizResults) {
                this.saveQuizResults(progressData.quizResults);
            }
            
            this.updateProgressDisplay();
            
            this.dispatchProgressEvent('progress:imported', { 
                importDate: new Date().toISOString() 
            });
            
            return true;
        } catch (error) {
            console.error('Failed to import progress:', error);
            return false;
        }
    }
    
    /**
     * Clear all progress data
     */
    clearProgress() {
        try {
            localStorage.removeItem(this.STORAGE_KEYS.READ_EVENTS);
            localStorage.removeItem(this.STORAGE_KEYS.QUIZ_RESULTS);
            
            this.updateProgressDisplay();
            
            this.dispatchProgressEvent('progress:cleared', {
                clearDate: new Date().toISOString()
            });
            
            console.log('Progress data cleared');
        } catch (error) {
            console.error('Failed to clear progress:', error);
        }
    }
    
    /**
     * Dispatch progress event
     * @param {string} eventType - Event type
     * @param {Object} detail - Event detail
     */
    dispatchProgressEvent(eventType, detail = {}) {
        const event = new CustomEvent(eventType, { detail });
        window.dispatchEvent(event);
    }
    
    /**
     * Check if event is read
     * @param {number} eventId - Event ID
     * @returns {boolean} Whether event is read
     */
    isEventRead(eventId) {
        return this.getReadEvents().includes(eventId);
    }
    
    /**
     * Get quiz result for event
     * @param {number} eventId - Event ID
     * @returns {Object|null} Quiz result or null
     */
    getQuizResult(eventId) {
        const quizResults = this.getQuizResults();
        return quizResults[eventId] || null;
    }
    
    /**
     * Destroy progress tracker
     */
    destroy() {
        this.isInitialized = false;
        console.log('Progress tracker destroyed');
    }
}

// Create singleton instance
const progressTracker = new ProgressTracker();

// Export for module use
export default progressTracker;
