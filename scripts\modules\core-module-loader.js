/**
 * Core Module Loader
 * Handles dynamic loading and initialization of application modules
 */

class CoreModuleLoader {
    constructor() {
        this.modules = new Map();
        this.modulesToLoad = [
            { name: 'errorHandler', path: './error-handling.js', required: true },
            { name: 'themeManager', path: './theme-manager.js', required: false },
            { name: 'searchEngine', path: './search.js', required: false },
            { name: 'timeline', path: './timeline.js', required: true },
            { name: 'eventRenderer', path: './event-rendering.js', required: true },
            { name: 'analysisPanel', path: './analysis.js', required: false },
            { name: 'quizEngine', path: './quiz.js', required: false },
            { name: 'progressTracker', path: './progress-tracker.js', required: false }
        ];
        this.loadedCount = 0;
        this.failedModules = [];
        this.isLoading = false;
    }
    
    /**
     * Load all modules asynchronously
     * @returns {Promise<boolean>} Success status
     */
    async loadModules() {
        if (this.isLoading) {
            console.warn('Module loading already in progress');
            return false;
        }
        
        this.isLoading = true;
        this.dispatchEvent('MODULE_LOADING_START');
        
        try {
            const promises = this.modulesToLoad.map(async (moduleConfig) => {
                try {
                    const moduleObj = await import(moduleConfig.path);
                    this.modules.set(moduleConfig.name, {
                        module: moduleObj.default || moduleObj,
                        config: moduleConfig
                    });
                    this.loadedCount++;
                    this.dispatchProgressEvent();
                    return { success: true, name: moduleConfig.name };
                } catch (error) {
                    console.error(`Failed to load module ${moduleConfig.name}:`, error);
                    this.failedModules.push({
                        name: moduleConfig.name,
                        error: error.message,
                        required: moduleConfig.required
                    });
                    
                    // If it's a required module, throw error
                    if (moduleConfig.required) {
                        throw new Error(`Required module ${moduleConfig.name} failed to load: ${error.message}`);
                    }
                    
                    return { success: false, name: moduleConfig.name, error };
                }
            });
            
            const results = await Promise.allSettled(promises);
            
            // Check if any required modules failed
            const requiredFailures = this.failedModules.filter(m => m.required);
            if (requiredFailures.length > 0) {
                throw new Error(`Required modules failed to load: ${requiredFailures.map(m => m.name).join(', ')}`);
            }
            
            this.registerModules();
            this.dispatchCompleteEvent();
            return true;
            
        } catch (error) {
            this.dispatchErrorEvent(error);
            return false;
        } finally {
            this.isLoading = false;
        }
    }
    
    /**
     * Register loaded modules to global App object
     */
    registerModules() {
        // Ensure App object exists
        if (typeof window.App === 'undefined') {
            window.App = {
                Modules: {},
                Events: {
                    MODULE_LOADED: 'app:module-loaded',
                    CORE_MODULES_LOADED: 'app:core-modules-loaded',
                    CORE_MODULES_LOAD_ERROR: 'app:core-modules-load-error',
                    MODULE_LOADING_START: 'app:module-loading-start'
                }
            };
        }
        
        for (const [name, moduleData] of this.modules) {
            window.App.Modules[name] = moduleData.module;
        }
        
        console.log(`Registered ${this.modules.size} modules:`, Array.from(this.modules.keys()));
    }
    
    /**
     * Get a specific module
     * @param {string} name - Module name
     * @returns {Object|null} Module object or null if not found
     */
    getModule(name) {
        const moduleData = this.modules.get(name);
        return moduleData ? moduleData.module : null;
    }
    
    /**
     * Check if a module is loaded
     * @param {string} name - Module name
     * @returns {boolean} Whether module is loaded
     */
    isModuleLoaded(name) {
        return this.modules.has(name);
    }
    
    /**
     * Get loading statistics
     * @returns {Object} Loading stats
     */
    getLoadingStats() {
        return {
            total: this.modulesToLoad.length,
            loaded: this.loadedCount,
            failed: this.failedModules.length,
            progress: Math.round((this.loadedCount / this.modulesToLoad.length) * 100),
            failedModules: this.failedModules
        };
    }
    
    /**
     * Dispatch progress event
     */
    dispatchProgressEvent() {
        const stats = this.getLoadingStats();
        this.dispatchEvent('MODULE_LOADED', stats);
    }
    
    /**
     * Dispatch completion event
     */
    dispatchCompleteEvent() {
        const stats = this.getLoadingStats();
        this.dispatchEvent('CORE_MODULES_LOADED', {
            ...stats,
            timestamp: Date.now()
        });
    }
    
    /**
     * Dispatch error event
     * @param {Error} error - Error object
     */
    dispatchErrorEvent(error) {
        const stats = this.getLoadingStats();
        this.dispatchEvent('CORE_MODULES_LOAD_ERROR', {
            ...stats,
            error: error.message,
            stack: error.stack
        });
    }
    
    /**
     * Dispatch custom event
     * @param {string} eventType - Event type
     * @param {Object} detail - Event detail data
     */
    dispatchEvent(eventType, detail = {}) {
        const eventName = window.App?.Events?.[eventType] || `app:${eventType.toLowerCase()}`;
        const event = new CustomEvent(eventName, { detail });
        window.dispatchEvent(event);
    }
}

// Create and expose module loader
const moduleLoader = new CoreModuleLoader();
window.CoreModuleLoader = moduleLoader;

// Auto-start loading when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        moduleLoader.loadModules();
    });
} else {
    // DOM already loaded
    moduleLoader.loadModules();
}

export default CoreModuleLoader;
