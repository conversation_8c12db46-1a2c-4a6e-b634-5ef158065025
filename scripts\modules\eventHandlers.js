// 事件处理器模块
const EventHandlers = {
    setupEventListeners: function() {
        // 标签切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                
                const tab = btn.dataset.tab;
                document.querySelectorAll('.perspective-content').forEach(content => {
                    content.style.display = content.dataset.perspective === tab ? 'block' : 'none';
                });
            });
        });
        
        // 问答按钮
        document.getElementById('quiz-btn').addEventListener('click', Quiz.startQuiz);
    }
};