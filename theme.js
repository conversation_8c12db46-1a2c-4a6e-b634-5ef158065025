// 初始化主题切换
function initializeThemeSwitcher() {
    const themeToggle = document.getElementById('theme-toggle');
    if (!themeToggle) return;

    // 检查用户偏好
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    document.body.classList.add(prefersDark ? 'dark-theme' : 'light-theme');

    themeToggle.addEventListener('click', () => {
        try {
            document.body.classList.toggle('dark-theme');
            document.body.classList.toggle('light-theme');
            
            // 保存用户偏好
            const isDark = document.body.classList.contains('dark-theme');
            localStorage.setItem('preferred-theme', isDark ? 'dark' : 'light');
        } catch (error) {
            console.error('Theme toggle failed:', error);
            showErrorNotification('主题切换功能暂时不可用');
        }
    });
}


// 初始化滚动进度指示器
function initScrollProgress() {
    const scrollIndicator = document.getElementById('scroll-indicator');
    if (!scrollIndicator) return;

    window.addEventListener('scroll', () => {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const scrollHeight = document.documentElement.scrollHeight - document.documentElement.clientHeight;
        const scrollPercent = scrollHeight > 0 ? (scrollTop / scrollHeight) * 100 : 0;
        
        scrollIndicator.style.width = `${scrollPercent}%`;
    });
}


// 返回顶部按钮功能
function setupBackToTop() {
    const backToTopBtn = document.getElementById('back-to-top');
    if (!backToTopBtn) return;

    // 滚动时显示/隐藏按钮
    window.addEventListener('scroll', () => {
        if (window.pageYOffset > 300) {
            backToTopBtn.classList.add('show');
        } else {
            backToTopBtn.classList.remove('show');
        }
    });

    // 点击返回顶部
    backToTopBtn.addEventListener('click', (e) => {
        e.preventDefault();
        window.scrollTo({ top: 0, behavior: 'smooth' });
    });
}
