// 分析面板模块
const Analysis = {
    updateAnalysisPanel: function(event) {
        const analysisContent = document.getElementById('analysis-content');
        analysisContent.innerHTML = '';
        
        document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        document.querySelector('[data-tab="philosophy"]').classList.add('active');
        
        const perspectives = ['philosophy', 'psychology', 'sociology', 'economics'];
        perspectives.forEach(perspective => {
            const contentDiv = document.createElement('div');
            contentDiv.className = `perspective-content ${perspective}`;
            contentDiv.dataset.perspective = perspective;
            contentDiv.innerHTML = `<h3>${event.analysis[perspective].title}</h3>
                                   <p>${event.analysis[perspective].content}</p>`;
            analysisContent.appendChild(contentDiv);
        });
        
        document.querySelector('.perspective-content').style.display = 'block';
    }
};