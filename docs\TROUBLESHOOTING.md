# 故障排除指南

## 常见问题

### 🚫 应用程序无法加载

#### 症状
- 页面显示空白
- 控制台显示模块加载错误
- 加载指示器一直显示

#### 可能原因和解决方案

1. **浏览器不支持ES6模块**
   ```
   解决方案：使用现代浏览器（Chrome 61+, Firefox 60+, Safari 11+, Edge 79+）
   ```

2. **文件服务器问题**
   ```bash
   # 确保使用HTTP服务器而不是直接打开文件
   python -m http.server 8000
   # 或
   npm run dev
   ```

3. **CORS错误**
   ```
   错误：Access to script at 'file://...' from origin 'null' has been blocked by CORS policy
   解决方案：必须通过HTTP服务器访问，不能直接打开HTML文件
   ```

4. **模块路径错误**
   ```javascript
   // 检查控制台是否有404错误
   // 确保所有模块文件都存在于正确路径
   ```

### 📱 移动设备显示问题

#### 症状
- 布局在移动设备上显示异常
- 触摸操作不响应
- 文字过小或过大

#### 解决方案

1. **检查视口设置**
   ```html
   <!-- 确保HTML中包含此meta标签 -->
   <meta name="viewport" content="width=device-width, initial-scale=1.0">
   ```

2. **清除浏览器缓存**
   - iOS Safari: 设置 > Safari > 清除历史记录和网站数据
   - Android Chrome: 设置 > 隐私和安全 > 清除浏览数据

3. **检查CSS媒体查询**
   ```css
   /* 确保响应式样式正确加载 */
   @media (max-width: 768px) {
       /* 移动设备样式 */
   }
   ```

### 🔍 搜索功能不工作

#### 症状
- 搜索框无响应
- 搜索结果不显示
- 搜索结果不准确

#### 解决方案

1. **检查数据加载**
   ```javascript
   // 在控制台检查
   console.log(window.allHistoricalEvents);
   // 应该显示历史事件数组
   ```

2. **重建搜索索引**
   ```javascript
   // 在控制台执行
   const searchEngine = window.HistoryApp.getModule('searchEngine');
   searchEngine.buildSearchIndex();
   ```

3. **检查搜索输入**
   - 确保输入至少2个字符
   - 检查是否有特殊字符导致问题

### 🎨 主题切换问题

#### 症状
- 主题切换按钮不工作
- 主题不保存
- 颜色显示异常

#### 解决方案

1. **检查本地存储**
   ```javascript
   // 检查主题偏好是否保存
   console.log(localStorage.getItem('preferred-theme'));
   ```

2. **清除主题缓存**
   ```javascript
   // 重置主题设置
   localStorage.removeItem('preferred-theme');
   location.reload();
   ```

3. **检查CSS变量支持**
   ```javascript
   // 检查浏览器是否支持CSS自定义属性
   const testEl = document.createElement('div');
   testEl.style.setProperty('--test', 'test');
   console.log(testEl.style.getPropertyValue('--test'));
   ```

### 📊 进度数据丢失

#### 症状
- 学习进度重置
- 测验成绩消失
- 已读状态丢失

#### 解决方案

1. **检查本地存储**
   ```javascript
   // 检查存储的数据
   console.log(localStorage.getItem('readEvents'));
   console.log(localStorage.getItem('quizResults'));
   ```

2. **恢复备份数据**
   ```javascript
   // 如果有导出的数据，可以重新导入
   const progressTracker = window.HistoryApp.getModule('progressTracker');
   progressTracker.importProgress(backupData);
   ```

3. **防止数据丢失**
   ```javascript
   // 定期导出数据
   const exportData = progressTracker.exportProgress();
   console.log('备份数据:', JSON.stringify(exportData));
   ```

### ♿ 无障碍功能问题

#### 症状
- 屏幕阅读器无法正确读取
- 键盘导航不工作
- 焦点指示器缺失

#### 解决方案

1. **启用键盘导航**
   ```javascript
   // 确保键盘导航已激活
   document.body.classList.add('keyboard-navigation');
   ```

2. **检查ARIA标签**
   ```javascript
   // 验证ARIA标签是否正确
   const elements = document.querySelectorAll('[aria-label], [aria-labelledby]');
   console.log('ARIA元素数量:', elements.length);
   ```

3. **测试屏幕阅读器**
   - Windows: 使用NVDA（免费）
   - macOS: 使用VoiceOver（内置）
   - 检查是否有实时公告

### 🚀 性能问题

#### 症状
- 页面加载缓慢
- 滚动卡顿
- 内存使用过高

#### 解决方案

1. **检查性能指标**
   ```javascript
   // 查看性能报告
   const performance = window.HistoryApp.getModule('performanceManager');
   console.log(performance.getPerformanceReport());
   ```

2. **优化图片加载**
   ```javascript
   // 确保懒加载正常工作
   const images = document.querySelectorAll('img[data-src]');
   console.log('待加载图片数量:', images.length);
   ```

3. **清理内存**
   ```javascript
   // 强制垃圾回收（仅在开发工具中有效）
   if (window.gc) {
       window.gc();
   }
   ```

## 调试工具

### 开发者控制台命令

```javascript
// 获取应用状态
window.HistoryApp.isReady()

// 查看所有模块
Object.keys(window.HistoryApp.modules)

// 获取特定模块
const searchEngine = window.HistoryApp.getModule('searchEngine');

// 查看错误统计
window.ErrorHandler.getErrorStats()

// 查看性能指标
window.HistoryApp.getModule('performanceManager').getMetrics()

// 查看进度数据
window.HistoryApp.getModule('progressTracker').getProgressSummary()

// 启用调试模式
window.DEBUG = true;
```

### 网络问题诊断

```javascript
// 检查网络连接
navigator.onLine

// 测试资源加载
fetch('./data/events.js')
    .then(response => console.log('数据加载成功'))
    .catch(error => console.error('数据加载失败:', error));

// 查看资源加载时间
performance.getEntriesByType('resource').forEach(entry => {
    console.log(entry.name, entry.duration + 'ms');
});
```

## 浏览器特定问题

### Chrome
- **问题**: 模块加载失败
- **解决**: 检查是否启用了实验性功能
- **设置**: chrome://flags/#enable-experimental-web-platform-features

### Firefox
- **问题**: CSS Grid布局异常
- **解决**: 更新到最新版本
- **检查**: about:support

### Safari
- **问题**: 某些ES6特性不支持
- **解决**: 使用polyfill或降级处理
- **检查**: 开发者菜单 > 错误控制台

### Edge
- **问题**: 兼容性问题
- **解决**: 确保使用Chromium版Edge (79+)
- **检查**: edge://version/

## 报告问题

### 收集信息

在报告问题前，请收集以下信息：

1. **浏览器信息**
   ```javascript
   console.log(navigator.userAgent);
   ```

2. **错误信息**
   ```javascript
   // 查看错误日志
   window.ErrorHandler.getErrorStats();
   ```

3. **性能信息**
   ```javascript
   // 获取性能报告
   window.HistoryApp.getModule('performanceManager').getPerformanceReport();
   ```

4. **应用状态**
   ```javascript
   // 检查应用状态
   console.log({
       isReady: window.HistoryApp.isReady(),
       modules: Object.keys(window.HistoryApp.modules),
       theme: window.HistoryApp.getModule('themeManager').getCurrentTheme()
   });
   ```

### 问题模板

```markdown
## 问题描述
[详细描述遇到的问题]

## 重现步骤
1. 
2. 
3. 

## 预期行为
[描述期望的正确行为]

## 实际行为
[描述实际发生的情况]

## 环境信息
- 浏览器: [浏览器名称和版本]
- 操作系统: [操作系统和版本]
- 设备类型: [桌面/移动/平板]

## 错误信息
[粘贴控制台错误信息]

## 附加信息
[任何其他相关信息]
```

## 联系支持

如果以上解决方案都无法解决问题，请：

1. **查看已知问题**: [GitHub Issues](https://github.com/project/issues)
2. **搜索解决方案**: 在问题列表中搜索相似问题
3. **提交新问题**: [创建新的问题报告](https://github.com/project/issues/new)
4. **联系开发团队**: [<EMAIL>](mailto:<EMAIL>)

---

**提示**: 在联系支持前，请尝试清除浏览器缓存和本地存储，这能解决大部分问题。
