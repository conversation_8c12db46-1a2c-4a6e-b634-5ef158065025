// 创建单个事件卡片
function createEventCard(event) {
    // 验证事件数据
    if (!event || !event.id) {
        console.warn('Invalid event data');
        return document.createElement('div');
    }
    
    const card = document.createElement('div');
    card.className = 'event-card';
    card.setAttribute('data-event-id', event.id);
    
    // 构建卡片HTML
    card.innerHTML = `
        <h3 class="event-title">${event.title}</h3>
        <p class="event-period">时期: ${event.period}</p>
        <p class="event-region">地区: ${event.region || '未知'}</p>
        <p class="event-description">${event.description.substring(0, 150)}...</p>
        <button class="view-details" data-id="${event.id}">查看详情</button>
    `;
    
    // 添加点击事件监听器
    const viewDetailsBtn = card.querySelector('.view-details');
    if (viewDetailsBtn) {
        viewDetailsBtn.addEventListener('click', () => {
            renderEventDetail(event.id);
        });
    }
    
    return card;
}


// 更新事件显示
function updateEventDisplay(events) {
    if (!events || events.length === 0) {
        const eventList = document.querySelector('.event-list');
        if (eventList) {
            eventList.innerHTML = '<p>未找到匹配的历史事件</p>';
        }
        return;
    }
    
    // 创建事件卡片
    const eventList = document.querySelector('.event-list');
    if (!eventList) {
        console.warn('Event list container not found');
        return;
    }
    
    // 清空现有内容
    eventList.innerHTML = '';
    
    // 为每个事件创建卡片
    events.forEach(event => {
        const card = createEventCard(event);
        eventList.appendChild(card);
    });
    
    // 触发自定义事件，通知事件列表已渲染完成
    const event = new Event('eventsRendered');
    document.dispatchEvent(event);
}
