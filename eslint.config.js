// eslint.config.js
export default [
  {
    files: ["**/*.js"],
    languageOptions: {
      ecmaVersion: 2022,
      sourceType: "module",
      globals: {
        window: "readonly",
        document: "readonly",
        console: "readonly",
        localStorage: "readonly",
        CustomEvent: "readonly",
        Event: "readonly",
        setTimeout: "readonly",
        clearTimeout: "readonly",
        setInterval: "readonly",
        clearInterval: "readonly"
      }
    },
    rules: {
      "no-console": ["warn"],
      "no-var": ["error"],
      "prefer-const": ["error"],
      "no-unused-vars": ["error", { "argsIgnorePattern": "^_" }],
      "no-undef": ["error"],
      "semi": ["error", "always"],
      "quotes": ["error", "single", { "allowTemplateLiterals": true }],
      "indent": ["error", 4],
      "comma-dangle": ["error", "never"],
      "object-curly-spacing": ["error", "always"],
      "array-bracket-spacing": ["error", "never"],
      "space-before-function-paren": ["error", "never"],
      "keyword-spacing": ["error"],
      "space-infix-ops": ["error"],
      "eol-last": ["error", "always"],
      "no-trailing-spaces": ["error"],
      "max-len": ["warn", { "code": 120 }]
    }
  },
  {
    files: ["data/**/*.js"],
    rules: {
      "no-console": "off",
      "max-len": "off"
    }
  },
  {
    ignores: [
      "**/events_part*.js",
      "node_modules/**",
      "dist/**",
      "build/**"
    ]
  }
];