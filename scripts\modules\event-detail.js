// 事件详情模块
/* eslint-env browser */
"use strict";

function renderEventDetail(eventId, event) {
    const eventDetailContainer = document.getElementById('event-detail');
    if (!eventDetailContainer) return;
    if (!event) return;

    const generateGlossaryHTML = (glossaryTerms) => {
        if (!glossaryTerms || Object.keys(glossaryTerms).length === 0) return '';
        return `
            <div class="glossary-section">
                <h4>术语解释</h4>
                <ul class="glossary-list">
                    ${Object.entries(glossaryTerms)
                        .map(([term, definition]) => 
                            `<li><strong>${term}:</strong> ${definition}</li>`
                        ).join('')}
                </ul>
            </div>
        `;
    };

    const generateAnalysisSectionHTML = (analysisType, analysisData) => {
        if (!analysisData || !analysisData.content) return '';
        return `
            <div class="analysis-section ${analysisType.toLowerCase()}-section">
                <h4>${analysisType}视角</h4>
                <p>${analysisData.content}</p>
                ${analysisData.questions && analysisData.questions.length > 0 
                    ? `<div class="analysis-questions">
                        <strong>思考问题:</strong>
                        <ul>
                            ${analysisData.questions.map(q => `<li>${q}</li>`).join('')}
                        </ul>
                      </div>` 
                    : ''}
            </div>
        `;
    };

    const generateImageGalleryHTML = () => {
        return `
            <div class="related-images">
                <h4>相关图片</h4>
                <div class="image-gallery">
                    <div class="image-card">
                        <img src="https://via.placeholder.com/600x400?text=${encodeURIComponent(event.title)}" 
                             alt="${event.title}" 
                             data-event-id="${event.id}">
                        <div class="image-info">
                            <div class="image-title">${event.title}</div>
                            <div class="image-period">时期: ${event.period}</div>
                            <div class="image-region">地区: ${event.region}</div>
                            <div class="image-description">${event.description.substring(0, 100)}...</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    };

    let content = `
        <div class="event-detail-card">
            <h3>${event.title}</h3>
            <div class="event-period">时期: ${event.period}</div>
            <div class="event-region">地区: ${event.region}</div>
            <div class="event-description">
                <strong>描述:</strong> ${event.description}
            </div>
            <div class="multi-dimension-analysis">
                ${generateAnalysisSectionHTML('哲学', event.philosophy)}
                ${generateAnalysisSectionHTML('心理学', event.psychology)}
                ${generateAnalysisSectionHTML('社会学', event.sociology)}
                ${generateAnalysisSectionHTML('金融经济学', event.economics)}
                ${generateAnalysisSectionHTML('AI洞察', event.aiInsights)}
            </div>
            ${generateGlossaryHTML(event.glossaryTerms)}
            ${generateImageGalleryHTML()}
        </div>
    `;

    eventDetailContainer.innerHTML = content;
    history.pushState(null, '', `#event-${eventId}`);
}

export { renderEventDetail };
