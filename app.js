/**
 * Main Application Entry Point
 * Orchestrates module loading and application initialization
 */

import errorHandler from './modules/error-handling.js';
import themeManager from './modules/theme-manager.js';
import performanceManager from './modules/performance.js';
import accessibilityManager from './modules/accessibility.js';
import securityManager from './modules/security.js';

class HistoryLearningApp {
    constructor() {
        this.isInitialized = false;
        this.modules = new Map();
        this.loadingIndicator = null;
        this.retryCount = 0;
        this.maxRetries = 3;
        
        // Bind methods
        this.init = this.init.bind(this);
        this.handleModuleLoadError = this.handleModuleLoadError.bind(this);
        this.handleRetry = this.handleRetry.bind(this);
    }
    
    /**
     * Initialize the application
     */
    async init() {
        if (this.isInitialized) {
            console.warn('Application already initialized');
            return;
        }
        
        try {
            console.log('Initializing History Learning Platform...');
            
            // Show loading indicator
            this.showLoadingIndicator();
            
            // Initialize core systems first
            await this.initializeCoreModules();
            
            // Load and initialize feature modules
            await this.loadFeatureModules();
            
            // Set up global event listeners
            this.setupGlobalEventListeners();
            
            // Initialize UI components
            this.initializeUI();
            
            // Hide loading indicator
            this.hideLoadingIndicator();
            
            this.isInitialized = true;
            console.log('Application initialized successfully');
            
            // Dispatch initialization complete event
            this.dispatchEvent('app:initialized');
            
        } catch (error) {
            console.error('Application initialization failed:', error);
            this.handleInitializationError(error);
        }
    }
    
    /**
     * Initialize core modules that are required for basic functionality
     */
    async initializeCoreModules() {
        console.log('Initializing core modules...');

        // Error handler is already initialized
        this.modules.set('errorHandler', errorHandler);

        // Theme manager is already initialized
        this.modules.set('themeManager', themeManager);

        // Initialize performance manager
        performanceManager.init();
        this.modules.set('performanceManager', performanceManager);

        // Initialize accessibility manager
        accessibilityManager.init();
        this.modules.set('accessibilityManager', accessibilityManager);

        // Initialize security manager
        securityManager.init();
        this.modules.set('securityManager', securityManager);

        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            await new Promise(resolve => {
                document.addEventListener('DOMContentLoaded', resolve);
            });
        }
    }
    
    /**
     * Load feature modules dynamically
     */
    async loadFeatureModules() {
        console.log('Loading feature modules...');
        
        const moduleConfigs = [
            { name: 'eventRenderer', path: './modules/event-rendering.js', required: true },
            { name: 'timeline', path: './modules/timeline.js', required: true },
            { name: 'searchEngine', path: './modules/search.js', required: false },
            { name: 'analysisPanel', path: './modules/analysis.js', required: false },
            { name: 'quizEngine', path: './modules/quiz.js', required: false },
            { name: 'progressTracker', path: './modules/progress-tracker.js', required: false }
        ];
        
        const loadPromises = moduleConfigs.map(async (config) => {
            try {
                const module = await import(config.path);
                this.modules.set(config.name, module.default || module);
                console.log(`Loaded module: ${config.name}`);
                return { success: true, name: config.name };
            } catch (error) {
                console.error(`Failed to load module ${config.name}:`, error);
                
                if (config.required) {
                    throw new Error(`Required module ${config.name} failed to load`);
                }
                
                return { success: false, name: config.name, error };
            }
        });
        
        const results = await Promise.allSettled(loadPromises);
        
        // Check for critical failures
        const failures = results
            .filter(result => result.status === 'rejected')
            .map(result => result.reason);
            
        if (failures.length > 0) {
            throw new Error(`Critical modules failed to load: ${failures.join(', ')}`);
        }
    }
    
    /**
     * Set up global event listeners
     */
    setupGlobalEventListeners() {
        // Module loading events
        window.addEventListener('app:module-loaded', (event) => {
            console.log('Module loaded:', event.detail);
        });
        
        // Theme change events
        window.addEventListener('themechange', (event) => {
            console.log('Theme changed:', event.detail);
        });
        
        // Error events
        window.addEventListener('app:error', (event) => {
            this.handleModuleLoadError(event.detail);
        });
        
        // Retry events
        const retryButton = document.getElementById('retry-button');
        if (retryButton) {
            retryButton.addEventListener('click', this.handleRetry);
        }
    }
    
    /**
     * Initialize UI components
     */
    initializeUI() {
        console.log('Initializing UI components...');
        
        // Initialize event rendering
        const eventRenderer = this.modules.get('eventRenderer');
        if (eventRenderer && typeof eventRenderer.init === 'function') {
            eventRenderer.init();
        }
        
        // Initialize timeline
        const timeline = this.modules.get('timeline');
        if (timeline && typeof timeline.init === 'function') {
            timeline.init();
        }
        
        // Initialize search
        const searchEngine = this.modules.get('searchEngine');
        if (searchEngine && typeof searchEngine.init === 'function') {
            searchEngine.init();
        }
        
        // Initialize analysis panel
        const analysisPanel = this.modules.get('analysisPanel');
        if (analysisPanel && typeof analysisPanel.init === 'function') {
            analysisPanel.init();
        }
        
        // Initialize quiz engine
        const quizEngine = this.modules.get('quizEngine');
        if (quizEngine && typeof quizEngine.init === 'function') {
            quizEngine.init();
        }
        
        // Initialize progress tracker
        const progressTracker = this.modules.get('progressTracker');
        if (progressTracker && typeof progressTracker.init === 'function') {
            progressTracker.init();
        }
    }
    
    /**
     * Show loading indicator
     */
    showLoadingIndicator() {
        this.loadingIndicator = document.getElementById('loading-indicator');
        if (this.loadingIndicator) {
            this.loadingIndicator.style.display = 'flex';
            this.loadingIndicator.setAttribute('aria-hidden', 'false');
        }
    }
    
    /**
     * Hide loading indicator
     */
    hideLoadingIndicator() {
        if (this.loadingIndicator) {
            this.loadingIndicator.style.display = 'none';
            this.loadingIndicator.setAttribute('aria-hidden', 'true');
        }
    }
    
    /**
     * Handle initialization errors
     * @param {Error} error - The error that occurred
     */
    handleInitializationError(error) {
        console.error('Initialization error:', error);
        
        this.hideLoadingIndicator();
        
        // Show error to user
        if (errorHandler) {
            errorHandler.handleError({
                type: 'initialization',
                message: error.message,
                stack: error.stack
            });
        }
    }
    
    /**
     * Handle module loading errors
     * @param {Object} errorDetail - Error details
     */
    handleModuleLoadError(errorDetail) {
        console.error('Module load error:', errorDetail);
        
        if (this.retryCount < this.maxRetries) {
            console.log(`Retrying initialization (${this.retryCount + 1}/${this.maxRetries})...`);
            this.retryCount++;
            setTimeout(() => this.init(), 1000);
        } else {
            console.error('Max retries reached. Application initialization failed.');
        }
    }
    
    /**
     * Handle retry button click
     */
    handleRetry() {
        this.retryCount = 0;
        this.isInitialized = false;
        this.init();
    }
    
    /**
     * Get a loaded module
     * @param {string} name - Module name
     * @returns {Object|null} Module instance or null
     */
    getModule(name) {
        return this.modules.get(name) || null;
    }
    
    /**
     * Check if application is initialized
     * @returns {boolean} Initialization status
     */
    isReady() {
        return this.isInitialized;
    }
    
    /**
     * Dispatch custom application event
     * @param {string} eventType - Event type
     * @param {Object} detail - Event detail data
     */
    dispatchEvent(eventType, detail = {}) {
        const event = new CustomEvent(eventType, { detail });
        window.dispatchEvent(event);
    }
    
    /**
     * Graceful shutdown
     */
    shutdown() {
        console.log('Shutting down application...');
        
        // Clean up modules
        this.modules.forEach((module, name) => {
            if (module && typeof module.destroy === 'function') {
                try {
                    module.destroy();
                    console.log(`Cleaned up module: ${name}`);
                } catch (error) {
                    console.error(`Error cleaning up module ${name}:`, error);
                }
            }
        });
        
        this.modules.clear();
        this.isInitialized = false;
        
        this.dispatchEvent('app:shutdown');
    }
}

// Create and initialize the application
const app = new HistoryLearningApp();

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => app.init());
} else {
    app.init();
}

// Export for global access
if (typeof window !== 'undefined') {
    // eslint-disable-next-line no-undef
    window.HistoryApp = app;
}

// Handle page visibility changes
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        console.log('Application paused');
    } else {
        console.log('Application resumed');
    }
});

// Handle page unload
window.addEventListener('beforeunload', () => {
    if (app.isReady()) {
        app.shutdown();
    }
});

export default HistoryLearningApp;
