# API 文档

## 全局对象

### window.HistoryApp

应用程序主实例，提供对所有模块的访问。

```javascript
// 获取应用程序实例
const app = window.HistoryApp;

// 检查应用程序是否就绪
if (app.isReady()) {
    console.log('应用程序已初始化');
}

// 获取特定模块
const searchEngine = app.getModule('searchEngine');
```

## 核心模块 API

### ErrorHandler

错误处理和用户反馈模块。

#### 方法

```javascript
// 手动处理错误
errorHandler.handleError({
    type: 'custom',
    message: '自定义错误消息',
    stack: new Error().stack
});

// 获取错误统计
const stats = errorHandler.getErrorStats();
console.log(`总错误数: ${stats.total}`);

// 清除错误记录
errorHandler.clearErrors();
```

#### 错误类型

| 类型 | 描述 | 示例 |
|------|------|------|
| `javascript` | JavaScript运行时错误 | 语法错误、类型错误 |
| `promise` | Promise拒绝错误 | 异步操作失败 |
| `resource` | 资源加载错误 | 图片、脚本加载失败 |
| `network` | 网络请求错误 | API调用失败 |
| `custom` | 自定义错误 | 业务逻辑错误 |

### ThemeManager

主题管理和切换模块。

#### 方法

```javascript
// 获取当前主题
const currentTheme = themeManager.getCurrentTheme();

// 设置主题
themeManager.setTheme('dark');

// 切换主题
themeManager.toggleTheme();

// 检查主题是否可用
if (themeManager.isThemeAvailable('dark')) {
    console.log('深色主题可用');
}

// 获取所有可用主题
const themes = themeManager.getAvailableThemes();
```

#### 事件

```javascript
// 监听主题变化
window.addEventListener('themechange', (e) => {
    console.log(`主题从 ${e.detail.oldTheme} 切换到 ${e.detail.newTheme}`);
});
```

### PerformanceManager

性能监控和优化模块。

#### 方法

```javascript
// 获取性能指标
const metrics = performanceManager.getMetrics();
console.log('加载时间:', metrics.loadTime);

// 获取完整性能报告
const report = performanceManager.getPerformanceReport();
console.log('Core Web Vitals:', report.coreWebVitals);

// 获取性能建议
const recommendations = performanceManager.getPerformanceRecommendations();
recommendations.forEach(rec => {
    console.log(`${rec.type}: ${rec.message}`);
});
```

#### 性能指标

```javascript
{
    loadTime: number,        // 页面加载时间 (ms)
    renderTime: number,      // 渲染时间 (ms)
    memoryUsage: number,     // 内存使用量 (bytes)
    entries: Array,          // 性能条目
    timestamp: number        // 时间戳
}
```

### AccessibilityManager

无障碍功能管理模块。

#### 方法

```javascript
// 发布屏幕阅读器公告
accessibilityManager.announce('内容已更新', 'polite');

// 获取无障碍报告
const report = accessibilityManager.getAccessibilityReport();
console.log('键盘导航激活:', report.keyboardNavigationActive);

// 聚焦主要内容
accessibilityManager.focusMainContent();

// 聚焦搜索框
accessibilityManager.focusSearchInput();
```

#### 键盘快捷键

| 快捷键 | 功能 |
|--------|------|
| `/` | 聚焦搜索框 |
| `Escape` | 关闭弹窗/返回主内容 |
| `Alt + H` | 聚焦主标题 |
| `Alt + M` | 聚焦主要内容 |

## 功能模块 API

### EventRenderer

历史事件渲染和显示模块。

#### 方法

```javascript
// 渲染事件列表
eventRenderer.renderEventList(events);

// 渲染事件详情
eventRenderer.renderEventDetail(eventId);

// 更新事件显示
eventRenderer.updateEventDisplay(filteredEvents);

// 获取当前事件
const currentEvents = eventRenderer.getCurrentEvents();

// 隐藏事件详情
eventRenderer.hideEventDetail();
```

#### 事件

```javascript
// 事件渲染完成
window.addEventListener('events:rendered', (e) => {
    console.log(`渲染了 ${e.detail.count} 个事件`);
});

// 事件详情渲染完成
window.addEventListener('event:detail-rendered', (e) => {
    console.log(`显示事件详情: ${e.detail.eventId}`);
});
```

### Timeline

时间轴可视化模块。

#### 方法

```javascript
// 选择时间轴事件
timeline.selectEvent(eventId);

// 滚动到特定事件
timeline.scrollToEvent(eventId);

// 获取时间轴统计
const stats = timeline.getStats();
console.log(`时间轴包含 ${stats.eventCount} 个事件`);
```

#### 事件

```javascript
// 时间轴事件选择
window.addEventListener('timeline:event-selected', (e) => {
    console.log(`选择了事件: ${e.detail.eventId}`);
});
```

### SearchEngine

搜索引擎模块。

#### 方法

```javascript
// 执行搜索
searchEngine.performSearch('认知革命');

// 清除搜索
searchEngine.clearSearch();

// 获取搜索统计
const stats = searchEngine.getStats();
console.log(`搜索索引大小: ${stats.indexSize}`);
```

#### 搜索配置

```javascript
// 搜索权重配置
const weights = {
    title: 10,      // 标题匹配权重
    period: 5,      // 时期匹配权重
    region: 3,      // 地区匹配权重
    content: 1      // 内容匹配权重
};
```

#### 事件

```javascript
// 搜索结果选择
window.addEventListener('search:result-selected', (e) => {
    console.log(`选择了搜索结果: ${e.detail.eventId}`);
});
```

### QuizEngine

测验系统模块。

#### 方法

```javascript
// 开始测验
quizEngine.startQuiz(eventId);

// 提交答案
quizEngine.submitAnswer(questionIndex, answerIndex);

// 完成测验
quizEngine.completeQuiz();

// 获取测验结果
const result = quizEngine.getQuizResult(eventId);
console.log(`得分: ${result.score}`);
```

#### 测验数据结构

```javascript
{
    eventId: number,
    questions: [{
        question: string,
        options: string[],
        answer: number,
        userAnswer?: number
    }],
    score: number,
    completed: boolean
}
```

#### 事件

```javascript
// 测验完成
window.addEventListener('quiz:completed', (e) => {
    console.log(`测验完成，得分: ${e.detail.score}`);
});

// 测验开始
window.addEventListener('quiz:started', (e) => {
    console.log(`开始测验: ${e.detail.eventId}`);
});
```

### ProgressTracker

学习进度跟踪模块。

#### 方法

```javascript
// 标记事件为已读
progressTracker.markEventAsRead(eventId);

// 记录测验结果
progressTracker.recordQuizResult(eventId, score);

// 获取进度摘要
const summary = progressTracker.getProgressSummary();
console.log(`完成度: ${summary.completion.percentage}%`);

// 获取事件进度
const eventProgress = progressTracker.getEventProgress(eventId);
console.log(`已读: ${eventProgress.isRead}`);

// 导出进度数据
const exportData = progressTracker.exportProgress();

// 导入进度数据
const success = progressTracker.importProgress(importData);

// 清除所有进度
progressTracker.clearProgress();
```

#### 进度数据结构

```javascript
{
    completion: {
        percentage: number,     // 完成百分比
        completed: number,      // 已完成数量
        total: number          // 总数量
    },
    quizzes: {
        completed: number,      // 完成的测验数量
        averageScore: number   // 平均分数
    },
    achievements: [{
        id: string,
        name: string,
        description: string
    }]
}
```

#### 事件

```javascript
// 事件标记为已读
window.addEventListener('progress:event-read', (e) => {
    console.log(`事件 ${e.detail.eventId} 已标记为已读`);
});

// 测验完成记录
window.addEventListener('progress:quiz-completed', (e) => {
    console.log(`记录测验结果: ${e.detail.score}`);
});
```

## 数据验证 API

### DataValidator

数据验证和清理模块。

#### 方法

```javascript
// 验证历史事件数据
const result = dataValidator.validateEvent(eventData);
if (!result.isValid) {
    console.error('验证失败:', result.errors);
}

// 验证事件集合
const collectionResult = dataValidator.validateEventCollection(events);
console.log(`有效事件: ${collectionResult.validEvents.length}`);

// 验证用户输入
const inputResult = dataValidator.validateUserInput(userInput, 'search');
if (inputResult.isValid) {
    console.log('清理后的输入:', inputResult.sanitized);
}
```

#### 验证规则

```javascript
// 事件验证规则
const eventRules = {
    id: { type: 'number', required: true, min: 1 },
    title: { type: 'string', required: true, minLength: 1, maxLength: 200 },
    period: { type: 'string', required: true, minLength: 1, maxLength: 100 },
    description: { type: 'string', required: true, minLength: 10, maxLength: 5000 }
};
```

## 安全 API

### SecurityManager

安全管理和输入清理模块。

#### 方法

```javascript
// 清理HTML内容
const cleanHTML = securityManager.sanitizeHTML(userHTML);

// 清理URL
const cleanURL = securityManager.sanitizeURL(userURL);

// 生成安全随机字符串
const randomString = securityManager.generateSecureRandom(16);

// 安全存储数据
const success = securityManager.secureLocalStorage('key', data);

// 获取安全报告
const report = securityManager.getSecurityReport();
console.log('CSP违规数量:', report.cspViolations);
```

## 事件系统

### 全局事件列表

| 事件名称 | 触发时机 | 数据结构 |
|---------|---------|----------|
| `app:initialized` | 应用初始化完成 | `{}` |
| `app:shutdown` | 应用关闭 | `{}` |
| `events:rendered` | 事件列表渲染 | `{count: number}` |
| `event:detail-rendered` | 事件详情渲染 | `{eventId: number}` |
| `search:result-selected` | 搜索结果选择 | `{eventId: number}` |
| `timeline:event-selected` | 时间轴事件选择 | `{eventId: number}` |
| `quiz:started` | 测验开始 | `{eventId: number}` |
| `quiz:completed` | 测验完成 | `{eventId: number, score: number}` |
| `progress:event-read` | 事件标记已读 | `{eventId: number}` |
| `progress:quiz-completed` | 测验结果记录 | `{eventId: number, score: number}` |
| `themechange` | 主题切换 | `{oldTheme: string, newTheme: string}` |
| `performance:load-measured` | 性能测量完成 | `{loadTime: number, ...}` |

### 事件监听示例

```javascript
// 监听多个事件
const events = [
    'events:rendered',
    'search:result-selected',
    'quiz:completed'
];

events.forEach(eventName => {
    window.addEventListener(eventName, (e) => {
        console.log(`事件: ${eventName}`, e.detail);
    });
});
```

## 配置选项

### 应用程序配置

```javascript
// 通过全局变量配置
window.APP_CONFIG = {
    debug: true,                    // 调试模式
    theme: 'auto',                  // 默认主题
    language: 'zh-CN',              // 语言设置
    performance: {
        enableMonitoring: true,     // 启用性能监控
        sampleRate: 0.1            // 采样率
    },
    accessibility: {
        announcements: true,        // 启用屏幕阅读器公告
        keyboardShortcuts: true    // 启用键盘快捷键
    }
};
```

## 错误处理

### 错误类型和处理

```javascript
try {
    // 可能出错的代码
    const result = someOperation();
} catch (error) {
    // 使用错误处理器
    if (window.ErrorHandler) {
        window.ErrorHandler.handleError({
            type: 'custom',
            message: error.message,
            stack: error.stack,
            context: 'someOperation'
        });
    }
}
```

### Promise错误处理

```javascript
someAsyncOperation()
    .catch(error => {
        window.ErrorHandler?.handleError({
            type: 'promise',
            message: error.message,
            operation: 'someAsyncOperation'
        });
    });
```

## 调试工具

### 开发者控制台命令

```javascript
// 获取应用状态
window.HistoryApp.getModule('performanceManager').getMetrics();

// 触发测试事件
window.dispatchEvent(new CustomEvent('test:event', {
    detail: { message: 'Test event triggered' }
}));

// 查看模块状态
Object.keys(window.HistoryApp.modules).forEach(name => {
    console.log(`${name}:`, window.HistoryApp.getModule(name));
});
```
