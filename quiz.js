// 初始化测验部分
function initQuizSection() {
    try {
        const quizContainer = document.getElementById('quiz-container');
        if (!quizContainer) {
            console.warn('Quiz container not found');
            return;
        }
        
        // 创建测验导航面板
        const navigationPanel = document.createElement('div');
        navigationPanel.className = 'quiz-navigation-panel';
        navigationPanel.innerHTML = `
            <h3>选择事件进行测验</h3>
            <div class="event-selection"></div>
        `;
        
        // 在测验内容之前插入导航面板
        const quizContent = quizContainer.querySelector('#quiz-content');
        if (quizContent) {
            quizContainer.insertBefore(navigationPanel, quizContent);
        } else {
            quizContainer.appendChild(navigationPanel);
        }
        
        // 填充事件选择列表
        populateEventSelection();
        
        // 添加测验完成监听器
        document.addEventListener('quizCompleted', () => {
            console.log('Quiz completed event received');
        });
    } catch (error) {
        console.error('Failed to initialize quiz section:', error);
    }
}

// 填充事件选择列表
function populateEventSelection() {
    const selectionContainer = document.querySelector('.event-selection');
    if (!selectionContainer || !allHistoricalEvents || allHistoricalEvents.length === 0) {
        return;
    }
    
    // 创建事件按钮容器
    const buttonsContainer = document.createElement('div');
    buttonsContainer.className = 'event-buttons';
    
    // 为每个事件创建按钮
    allHistoricalEvents.forEach(event => {
        const button = document.createElement('button');
        button.className = 'event-quiz-button';
        button.textContent = event.title;
        button.setAttribute('data-id', event.id);
        
        // 添加ARIA属性增强可访问性
        button.setAttribute('aria-label', `开始${event.title}的测验`);
        
        // 添加点击事件
        button.addEventListener('click', () => {
            renderQuizForEvent(event.id);
        });
        
        buttonsContainer.appendChild(button);
    });
    
    // 清空现有内容并添加新内容
    selectionContainer.innerHTML = '';
    selectionContainer.appendChild(buttonsContainer);
}

// 根据事件ID渲染测验
function renderQuizForEvent(eventId) {
    // 验证参数
    if (!eventId || isNaN(parseInt(eventId)) || parseInt(eventId) <= 0) {
        console.warn(`Invalid event ID: ${eventId}`);
        return;
    }
    
    // 查找事件
    const event = allHistoricalEvents.find(e => e.id === parseInt(eventId));
    if (!event) {
        console.warn(`Event with ID ${eventId} not found`);
        return;
    }
    
    // 显示测验
    generateQuiz(event, `quiz-${event.id}`);
    
    // 滚动到测验部分
    const quizContainer = document.getElementById('quiz-container');
    if (quizContainer) {
        quizContainer.scrollIntoView({ behavior: 'smooth' });
    }
}
