/**
 * Enhanced History Learning Platform - Main Application
 * Integrates all modules and manages application lifecycle
 */

import ErrorHandling from './modules/error-handling.js';
import ThemeManager from './modules/theme-manager.js';
import SearchEngine from './modules/search.js';
import TimelineRenderer from './modules/timeline.js';
import EventRenderer from './modules/event-rendering.js';
import AnalysisEngine from './modules/analysis.js';
import quizEngine from './modules/quiz.js';
import ProgressTracker from './modules/progress-tracker.js';

class HistoryLearningApp {
    constructor() {
        this.isInitialized = false;
        this.modules = new Map();
        this.eventListeners = [];
        this.currentEvent = null;
        this.appState = {
            theme: 'light',
            currentView: 'timeline',
            searchQuery: '',
            selectedEvent: null,
            analysisTab: 'philosophy'
        };
    }

    /**
     * Initialize the application
     */
    async init() {
        try {
            console.log('🚀 Initializing History Learning Platform...');

            // Show loading indicator
            this.showLoadingIndicator();

            // Initialize error handling first
            await this.initializeModule('errorHandling', ErrorHandling);

            // Initialize core modules
            await this.initializeModule('themeManager', ThemeManager);
            await this.initializeModule('searchEngine', SearchEngine);
            await this.initializeModule('timelineRenderer', TimelineRenderer);
            await this.initializeModule('eventRenderer', EventRenderer);
            await this.initializeModule('analysisEngine', AnalysisEngine);
            await this.initializeModule('quizEngine', quizEngine);
            await this.initializeModule('progressTracker', ProgressTracker);

            // Set up global event listeners
            this.setupGlobalEventListeners();

            // Set up keyboard shortcuts
            this.setupKeyboardShortcuts();

            // Initialize UI components
            this.initializeUI();

            // Load initial data
            await this.loadInitialData();

            // Hide loading indicator
            this.hideLoadingIndicator();

            this.isInitialized = true;
            console.log('✅ Application initialized successfully');

            // Dispatch initialization complete event
            this.dispatchAppEvent('app:initialized');

        } catch (error) {
            console.error('❌ Application initialization failed:', error);
            this.handleInitializationError(error);
        }
    }

    /**
     * Initialize a module
     * @param {string} name - Module name
     * @param {Object} module - Module instance
     */
    async initializeModule(name, module) {
        try {
            console.log(`📦 Initializing ${name}...`);

            if (module.init && typeof module.init === 'function') {
                await module.init();
            }

            this.modules.set(name, module);
            console.log(`✅ ${name} initialized`);

        } catch (error) {
            console.error(`❌ Failed to initialize ${name}:`, error);
            throw new Error(`Module initialization failed: ${name}`);
        }
    }

    /**
     * Set up global event listeners
     */
    setupGlobalEventListeners() {
        // Window events
        this.addEventListener(window, 'error', this.handleGlobalError.bind(this));
        this.addEventListener(window, 'unhandledrejection', this.handleUnhandledRejection.bind(this));
        this.addEventListener(window, 'beforeunload', this.handleBeforeUnload.bind(this));
        this.addEventListener(window, 'resize', this.handleWindowResize.bind(this));

        // Custom app events
        this.addEventListener(window, 'event:selected', this.handleEventSelected.bind(this));
        this.addEventListener(window, 'quiz:completed', this.handleQuizCompleted.bind(this));
        this.addEventListener(window, 'achievement:unlocked', this.handleAchievementUnlocked.bind(this));
        this.addEventListener(window, 'progress:updated', this.handleProgressUpdated.bind(this));

        // Theme events
        this.addEventListener(window, 'theme:changed', this.handleThemeChanged.bind(this));

        // Search events
        this.addEventListener(window, 'search:performed', this.handleSearchPerformed.bind(this));
        this.addEventListener(window, 'search:cleared', this.handleSearchCleared.bind(this));
    }

    /**
     * Set up keyboard shortcuts
     */
    setupKeyboardShortcuts() {
        this.addEventListener(document, 'keydown', (e) => {
            // Search shortcut (/)
            if (e.key === '/' && !e.ctrlKey && !e.metaKey && !e.altKey) {
                e.preventDefault();
                const searchInput = document.getElementById('search-input');
                if (searchInput) {
                    searchInput.focus();
                }
            }

            // Escape key - close modals
            if (e.key === 'Escape') {
                this.closeAllModals();
            }

            // Theme toggle (Ctrl/Cmd + Shift + T)
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'T') {
                e.preventDefault();
                this.modules.get('themeManager')?.toggleTheme();
            }

            // Quiz shortcut (Ctrl/Cmd + Q)
            if ((e.ctrlKey || e.metaKey) && e.key === 'q') {
                e.preventDefault();
                this.startQuizForCurrentEvent();
            }
        });
    }

    /**
     * Initialize UI components
     */
    initializeUI() {
        // Set up footer buttons
        this.setupFooterButtons();

        // Set up modal handlers
        this.setupModalHandlers();

        // Set up accessibility features
        this.setupAccessibilityFeatures();

        // Initialize tooltips
        this.initializeTooltips();
    }

    /**
     * Set up footer buttons
     */
    setupFooterButtons() {
        const quizBtn = document.getElementById('quiz-btn');
        const achievementsBtn = document.getElementById('achievements-btn');
        const exportBtn = document.getElementById('export-progress-btn');

        if (quizBtn) {
            this.addEventListener(quizBtn, 'click', () => {
                this.startQuizForCurrentEvent();
            });
        }

        if (achievementsBtn) {
            this.addEventListener(achievementsBtn, 'click', () => {
                this.showAchievements();
            });
        }

        if (exportBtn) {
            this.addEventListener(exportBtn, 'click', () => {
                this.exportProgress();
            });
        }
    }

    /**
     * Set up modal handlers
     */
    setupModalHandlers() {
        // Quiz modal
        const quizContainer = document.getElementById('quiz-container');
        if (quizContainer) {
            this.addEventListener(quizContainer, 'click', (e) => {
                if (e.target === quizContainer) {
                    this.closeQuiz();
                }
            });
        }

        // Achievements modal
        const achievementsModal = document.getElementById('achievements-modal');
        if (achievementsModal) {
            this.addEventListener(achievementsModal, 'click', (e) => {
                if (e.target === achievementsModal) {
                    this.closeAchievements();
                }
            });

            const closeBtn = achievementsModal.querySelector('.modal-close');
            if (closeBtn) {
                this.addEventListener(closeBtn, 'click', () => {
                    this.closeAchievements();
                });
            }
        }
    }

    /**
     * Set up accessibility features
     */
    setupAccessibilityFeatures() {
        // Add keyboard navigation support
        document.body.classList.add('keyboard-navigation');

        // Set up focus management
        this.setupFocusManagement();

        // Set up ARIA live regions
        this.setupAriaLiveRegions();
    }

    /**
     * Set up focus management
     */
    setupFocusManagement() {
        // Track focus for keyboard navigation
        let isKeyboardUser = false;

        this.addEventListener(document, 'keydown', (e) => {
            if (e.key === 'Tab') {
                isKeyboardUser = true;
                document.body.classList.add('keyboard-user');
            }
        });

        this.addEventListener(document, 'mousedown', () => {
            isKeyboardUser = false;
            document.body.classList.remove('keyboard-user');
        });
    }

    /**
     * Set up ARIA live regions
     */
    setupAriaLiveRegions() {
        // Create live region for announcements
        const liveRegion = document.createElement('div');
        liveRegion.setAttribute('aria-live', 'polite');
        liveRegion.setAttribute('aria-atomic', 'true');
        liveRegion.className = 'visually-hidden';
        liveRegion.id = 'live-region';
        document.body.appendChild(liveRegion);
    }

    /**
     * Initialize tooltips
     */
    initializeTooltips() {
        // Add tooltip functionality for elements with data-tooltip
        const tooltipElements = document.querySelectorAll('[data-tooltip]');
        tooltipElements.forEach(element => {
            this.setupTooltip(element);
        });
    }

    /**
     * Set up tooltip for element
     * @param {Element} element - Element to add tooltip to
     */
    setupTooltip(element) {
        let tooltip = null;

        const showTooltip = () => {
            const text = element.getAttribute('data-tooltip');
            if (!text) return;

            tooltip = document.createElement('div');
            tooltip.className = 'tooltip';
            tooltip.textContent = text;
            document.body.appendChild(tooltip);

            const rect = element.getBoundingClientRect();
            tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
            tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';
        };

        const hideTooltip = () => {
            if (tooltip) {
                tooltip.remove();
                tooltip = null;
            }
        };

        this.addEventListener(element, 'mouseenter', showTooltip);
        this.addEventListener(element, 'mouseleave', hideTooltip);
        this.addEventListener(element, 'focus', showTooltip);
        this.addEventListener(element, 'blur', hideTooltip);
    }

    /**
     * Load initial data
     */
    async loadInitialData() {
        try {
            // Check if events data is available
            if (typeof window.allHistoricalEvents === 'undefined') {
                throw new Error('Historical events data not loaded');
            }

            // Initialize timeline with events
            const timelineRenderer = this.modules.get('timelineRenderer');
            if (timelineRenderer) {
                timelineRenderer.renderTimeline(window.allHistoricalEvents);
            }

            // Initialize event list
            const eventRenderer = this.modules.get('eventRenderer');
            if (eventRenderer) {
                eventRenderer.renderEventList(window.allHistoricalEvents);
            }

            // Initialize progress tracking
            const progressTracker = this.modules.get('progressTracker');
            if (progressTracker) {
                progressTracker.updateProgressDisplay();
            }

            console.log('📊 Initial data loaded successfully');

        } catch (error) {
            console.error('❌ Failed to load initial data:', error);
            throw error;
        }
    }

    /**
     * Add event listener and track it for cleanup
     * @param {Element} element - Element to add listener to
     * @param {string} event - Event type
     * @param {Function} handler - Event handler
     * @param {Object} options - Event options
     */
    addEventListener(element, event, handler, options = {}) {
        element.addEventListener(event, handler, options);
        this.eventListeners.push({ element, event, handler, options });
    }

    /**
     * Show loading indicator
     */
    showLoadingIndicator() {
        const indicator = document.getElementById('loading-indicator');
        if (indicator) {
            indicator.style.display = 'flex';
            indicator.setAttribute('aria-hidden', 'false');
        }
    }

    /**
     * Hide loading indicator
     */
    hideLoadingIndicator() {
        const indicator = document.getElementById('loading-indicator');
        if (indicator) {
            indicator.style.display = 'none';
            indicator.setAttribute('aria-hidden', 'true');
        }
    }

    /**
     * Handle initialization error
     * @param {Error} error - Initialization error
     */
    handleInitializationError(error) {
        console.error('Initialization error:', error);

        const errorBoundary = document.getElementById('error-boundary');
        const errorMessage = document.getElementById('error-message');

        if (errorBoundary && errorMessage) {
            errorMessage.textContent = `应用程序初始化失败: ${error.message}`;
            errorBoundary.style.display = 'block';
        }

        this.hideLoadingIndicator();
    }

    /**
     * Handle global errors
     * @param {ErrorEvent} event - Error event
     */
    handleGlobalError(event) {
        console.error('Global error:', event.error);

        const errorHandling = this.modules.get('errorHandling');
        if (errorHandling) {
            errorHandling.handleError(event.error, 'Global Error');
        }
    }

    /**
     * Handle unhandled promise rejections
     * @param {PromiseRejectionEvent} event - Promise rejection event
     */
    handleUnhandledRejection(event) {
        console.error('Unhandled promise rejection:', event.reason);

        const errorHandling = this.modules.get('errorHandling');
        if (errorHandling) {
            errorHandling.handleError(event.reason, 'Unhandled Promise Rejection');
        }
    }

    /**
     * Handle before unload
     * @param {BeforeUnloadEvent} event - Before unload event
     */
    handleBeforeUnload(event) {
        // Save any pending data
        const progressTracker = this.modules.get('progressTracker');
        if (progressTracker) {
            progressTracker.saveProgress();
        }
    }

    /**
     * Handle window resize
     */
    handleWindowResize() {
        // Debounce resize handling
        clearTimeout(this.resizeTimeout);
        this.resizeTimeout = setTimeout(() => {
            this.dispatchAppEvent('app:resize');
        }, 250);
    }

    /**
     * Handle event selection
     * @param {CustomEvent} event - Event selection event
     */
    handleEventSelected(event) {
        const { eventId } = event.detail;
        this.currentEvent = eventId;

        // Mark event as read
        const progressTracker = this.modules.get('progressTracker');
        if (progressTracker) {
            progressTracker.markEventAsRead(eventId);
        }

        // Update analysis panel
        const analysisEngine = this.modules.get('analysisEngine');
        if (analysisEngine) {
            const eventData = window.allHistoricalEvents.find(e => e.id === eventId);
            if (eventData) {
                analysisEngine.displayAnalysis(eventData);
            }
        }
    }

    /**
     * Handle quiz completion
     * @param {CustomEvent} event - Quiz completion event
     */
    handleQuizCompleted(event) {
        const { eventId, score, percentage } = event.detail;

        // Update progress
        const progressTracker = this.modules.get('progressTracker');
        if (progressTracker) {
            progressTracker.recordQuizResult(eventId, score, percentage);
        }

        // Announce completion
        this.announceToScreenReader(`测验完成！得分 ${percentage}%`);
    }

    /**
     * Handle achievement unlock
     * @param {CustomEvent} event - Achievement unlock event
     */
    handleAchievementUnlocked(event) {
        const { achievement } = event.detail;
        this.announceToScreenReader(`成就解锁：${achievement.name}`);
    }

    /**
     * Handle progress update
     * @param {CustomEvent} event - Progress update event
     */
    handleProgressUpdated(event) {
        // Update UI elements that depend on progress
        this.updateProgressDependentUI();
    }

    /**
     * Handle theme change
     * @param {CustomEvent} event - Theme change event
     */
    handleThemeChanged(event) {
        const { theme } = event.detail;
        this.appState.theme = theme;
        this.announceToScreenReader(`主题已切换到${theme === 'dark' ? '深色' : '浅色'}模式`);
    }

    /**
     * Handle search performed
     * @param {CustomEvent} event - Search event
     */
    handleSearchPerformed(event) {
        const { query, results } = event.detail;
        this.appState.searchQuery = query;
        this.announceToScreenReader(`搜索"${query}"找到${results.length}个结果`);
    }

    /**
     * Handle search cleared
     */
    handleSearchCleared() {
        this.appState.searchQuery = '';
        this.announceToScreenReader('搜索已清除');
    }

    /**
     * Start quiz for current event
     */
    startQuizForCurrentEvent() {
        if (!this.currentEvent) {
            this.announceToScreenReader('请先选择一个历史事件');
            return;
        }

        const quizEngine = this.modules.get('quizEngine');
        if (quizEngine) {
            quizEngine.startQuiz(this.currentEvent);
            this.showQuizContainer();
        }
    }

    /**
     * Show quiz container
     */
    showQuizContainer() {
        const container = document.getElementById('quiz-container');
        if (container) {
            container.style.display = 'flex';
            container.setAttribute('aria-hidden', 'false');

            // Focus first interactive element
            const firstInput = container.querySelector('input, button');
            if (firstInput) {
                firstInput.focus();
            }
        }
    }

    /**
     * Close quiz
     */
    closeQuiz() {
        const container = document.getElementById('quiz-container');
        if (container) {
            container.style.display = 'none';
            container.setAttribute('aria-hidden', 'true');
        }

        const quizEngine = this.modules.get('quizEngine');
        if (quizEngine) {
            quizEngine.closeQuiz();
        }
    }

    /**
     * Show achievements modal
     */
    showAchievements() {
        const modal = document.getElementById('achievements-modal');
        if (modal) {
            modal.style.display = 'flex';
            modal.setAttribute('aria-hidden', 'false');

            // Focus close button
            const closeBtn = modal.querySelector('.modal-close');
            if (closeBtn) {
                closeBtn.focus();
            }
        }

        // Load achievements data
        this.loadAchievements();
    }

    /**
     * Close achievements modal
     */
    closeAchievements() {
        const modal = document.getElementById('achievements-modal');
        if (modal) {
            modal.style.display = 'none';
            modal.setAttribute('aria-hidden', 'true');
        }
    }

    /**
     * Load and display achievements
     */
    loadAchievements() {
        const progressTracker = this.modules.get('progressTracker');
        if (!progressTracker) return;

        const stats = progressTracker.calculateProgressStats();
        const achievements = progressTracker.calculateAchievements(stats);
        const unlockedAchievements = progressTracker.getUnlockedAchievements();

        const grid = document.querySelector('.achievements-grid');
        if (grid) {
            grid.innerHTML = this.renderAchievements(achievements, unlockedAchievements);
        }
    }

    /**
     * Render achievements HTML
     * @param {Array} achievements - All achievements
     * @param {Array} unlocked - Unlocked achievement IDs
     * @returns {string} HTML string
     */
    renderAchievements(achievements, unlocked) {
        return achievements.map(achievement => `
            <div class="achievement-card ${achievement.rarity} ${unlocked.includes(achievement.id) ? 'unlocked' : 'achievement-locked'}">
                <div class="achievement-card-header">
                    <div class="achievement-card-icon">${achievement.name.split(' ')[0]}</div>
                    <h3 class="achievement-card-title">${achievement.name.substring(2)}</h3>
                </div>
                <p class="achievement-card-description">${achievement.description}</p>
                <div class="achievement-card-footer">
                    <span class="achievement-rarity ${achievement.rarity}">${achievement.rarity}</span>
                    <span class="achievement-points">+${achievement.points} 积分</span>
                </div>
            </div>
        `).join('');
    }

    /**
     * Export progress data
     */
    exportProgress() {
        const progressTracker = this.modules.get('progressTracker');
        if (!progressTracker) return;

        const data = progressTracker.exportProgressData();
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `history-learning-progress-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.announceToScreenReader('学习进度已导出');
    }

    /**
     * Close all modals
     */
    closeAllModals() {
        this.closeQuiz();
        this.closeAchievements();
    }

    /**
     * Update progress dependent UI
     */
    updateProgressDependentUI() {
        // Update any UI elements that depend on progress
        const progressTracker = this.modules.get('progressTracker');
        if (progressTracker) {
            progressTracker.updateProgressDisplay();
        }
    }

    /**
     * Announce message to screen readers
     * @param {string} message - Message to announce
     */
    announceToScreenReader(message) {
        const liveRegion = document.getElementById('live-region');
        if (liveRegion) {
            liveRegion.textContent = message;

            // Clear after announcement
            setTimeout(() => {
                liveRegion.textContent = '';
            }, 1000);
        }
    }

    /**
     * Dispatch app event
     * @param {string} eventType - Event type
     * @param {Object} detail - Event detail
     */
    dispatchAppEvent(eventType, detail = {}) {
        const event = new CustomEvent(eventType, { detail });
        window.dispatchEvent(event);
    }

    /**
     * Destroy the application
     */
    destroy() {
        // Remove all event listeners
        this.eventListeners.forEach(({ element, event, handler, options }) => {
            element.removeEventListener(event, handler, options);
        });
        this.eventListeners = [];

        // Destroy modules
        this.modules.forEach((module, name) => {
            if (module.destroy && typeof module.destroy === 'function') {
                module.destroy();
            }
        });
        this.modules.clear();

        this.isInitialized = false;
        console.log('🔄 Application destroyed');
    }
}

// Create and initialize the application
const app = new HistoryLearningApp();

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => app.init());
} else {
    app.init();
}

// Export for global access
window.historyApp = app;

// Legacy support - old event data removed, now using data/events.js
