/**
 * Enhanced Quiz Engine Module
 * Supports multiple question types and advanced quiz features
 */

class QuizEngine {
    constructor() {
        this.currentQuiz = null;
        this.quizContainer = null;
        this.isInitialized = false;
        this.questionTypes = {
            MULTIPLE_CHOICE: 'multiple_choice',
            TRUE_FALSE: 'true_false',
            DRAG_DROP: 'drag_drop',
            FILL_BLANK: 'fill_blank',
            MATCHING: 'matching'
        };
        this.quizHistory = [];
        this.achievements = [];
    }

    /**
     * Initialize quiz engine
     */
    init() {
        if (this.isInitialized) return;

        // Get quiz container
        this.quizContainer = document.getElementById('quiz-container');

        // Set up event listeners
        this.setupEventListeners();

        // Load quiz history
        this.loadQuizHistory();

        this.isInitialized = true;
        console.log('Quiz engine initialized');
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Listen for quiz start requests
        window.addEventListener('quiz:start', (e) => {
            this.startQuiz(e.detail.eventId);
        });

        // Listen for quiz button clicks
        document.addEventListener('click', (e) => {
            if (e.target.matches('#quiz-btn, .start-quiz')) {
                const eventId = e.target.dataset.eventId;
                if (eventId) {
                    this.startQuiz(parseInt(eventId));
                }
            }
        });
    }

    /**
     * Start a quiz for a specific event
     * @param {number} eventId - Event ID
     */
    startQuiz(eventId) {
        const event = this.findEventById(eventId);
        if (!event || !event.quiz) {
            console.warn(`No quiz found for event ${eventId}`);
            return;
        }

        this.currentQuiz = this.generateQuiz(event);
        this.displayQuiz();

        // Dispatch quiz start event
        this.dispatchQuizEvent('quiz:started', {
            eventId: eventId,
            questionCount: this.currentQuiz.questions.length
        });
    }

    /**
     * Generate quiz object from event data
     * @param {Object} event - Event object
     * @returns {Object} Quiz object
     */
    generateQuiz(event) {
        const questions = this.processQuestions(event.quiz.questions);

        return {
            eventId: event.id,
            eventTitle: event.title,
            questions: questions,
            currentQuestion: 0,
            score: 0,
            answers: new Array(questions.length).fill(null),
            startTime: Date.now(),
            timeLimit: event.quiz.timeLimit || null,
            attempts: this.getAttemptCount(event.id) + 1
        };
    }

    /**
     * Process questions to add type information and validation
     * @param {Array} questions - Raw questions array
     * @returns {Array} Processed questions
     */
    processQuestions(questions) {
        return questions.map((question, index) => {
            const processedQuestion = {
                id: index,
                ...question,
                type: question.type || this.questionTypes.MULTIPLE_CHOICE,
                points: question.points || 1,
                explanation: question.explanation || null,
                hints: question.hints || []
            };

            // Shuffle options for multiple choice questions
            if (processedQuestion.type === this.questionTypes.MULTIPLE_CHOICE) {
                processedQuestion.shuffledOptions = this.shuffleOptions(
                    processedQuestion.options,
                    processedQuestion.answer
                );
            }

            return processedQuestion;
        });
    }

    /**
     * Shuffle options while maintaining correct answer index
     * @param {Array} options - Original options
     * @param {number} correctIndex - Original correct answer index
     * @returns {Object} Shuffled options with new correct index
     */
    shuffleOptions(options, correctIndex) {
        const correctAnswer = options[correctIndex];
        const shuffled = [...options].sort(() => Math.random() - 0.5);
        const newCorrectIndex = shuffled.indexOf(correctAnswer);

        return {
            options: shuffled,
            correctIndex: newCorrectIndex
        };
    }

    /**
     * Display current quiz question
     */
    displayQuiz() {
        if (!this.currentQuiz || !this.quizContainer) return;

        const question = this.currentQuiz.questions[this.currentQuiz.currentQuestion];
        const progress = ((this.currentQuiz.currentQuestion + 1) / this.currentQuiz.questions.length) * 100;

        this.quizContainer.innerHTML = `
            <div class="quiz-header">
                <h2>📝 ${this.currentQuiz.eventTitle} - 测验</h2>
                <div class="quiz-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${progress}%"></div>
                    </div>
                    <span class="progress-text">
                        问题 ${this.currentQuiz.currentQuestion + 1} / ${this.currentQuiz.questions.length}
                    </span>
                </div>
            </div>

            <div class="quiz-content">
                ${this.renderQuestion(question)}
            </div>

            <div class="quiz-controls">
                ${this.currentQuiz.currentQuestion > 0 ?
                    '<button class="quiz-btn secondary" onclick="quizEngine.previousQuestion()">上一题</button>' :
                    ''
                }
                <button class="quiz-btn primary" onclick="quizEngine.nextQuestion()"
                        ${this.isAnswerSelected() ? '' : 'disabled'}>
                    ${this.currentQuiz.currentQuestion < this.currentQuiz.questions.length - 1 ? '下一题' : '完成测验'}
                </button>
            </div>
        `;

        // Set up question-specific interactions
        this.setupQuestionInteractions(question);
    }

    /**
     * Render question based on its type
     * @param {Object} question - Question object
     * @returns {string} HTML string
     */
    renderQuestion(question) {
        switch (question.type) {
            case this.questionTypes.MULTIPLE_CHOICE:
                return this.renderMultipleChoice(question);
            case this.questionTypes.TRUE_FALSE:
                return this.renderTrueFalse(question);
            default:
                return this.renderMultipleChoice(question);
        }
    }

    /**
     * Render multiple choice question
     * @param {Object} question - Question object
     * @returns {string} HTML string
     */
    renderMultipleChoice(question) {
        const options = question.shuffledOptions || {
            options: question.options,
            correctIndex: question.answer
        };

        return `
            <div class="question-container">
                <h3 class="quiz-question">${question.question}</h3>

                <div class="quiz-options" role="radiogroup" aria-labelledby="question-${question.id}">
                    ${options.options.map((option, index) => `
                        <label class="quiz-option" for="option-${index}">
                            <input type="radio"
                                   id="option-${index}"
                                   name="quiz-answer"
                                   value="${index}"
                                   ${this.currentQuiz.answers[this.currentQuiz.currentQuestion] === index ? 'checked' : ''}>
                            <span class="option-text">${option}</span>
                            <span class="option-indicator"></span>
                        </label>
                    `).join('')}
                </div>
            </div>
        `;
    }

    /**
     * Render true/false question
     * @param {Object} question - Question object
     * @returns {string} HTML string
     */
    renderTrueFalse(question) {
        return `
            <div class="question-container">
                <h3 class="quiz-question">${question.question}</h3>

                <div class="quiz-options true-false" role="radiogroup">
                    <label class="quiz-option true-option" for="option-true">
                        <input type="radio"
                               id="option-true"
                               name="quiz-answer"
                               value="true"
                               ${this.currentQuiz.answers[this.currentQuiz.currentQuestion] === true ? 'checked' : ''}>
                        <span class="option-text">✓ 正确</span>
                        <span class="option-indicator"></span>
                    </label>

                    <label class="quiz-option false-option" for="option-false">
                        <input type="radio"
                               id="option-false"
                               name="quiz-answer"
                               value="false"
                               ${this.currentQuiz.answers[this.currentQuiz.currentQuestion] === false ? 'checked' : ''}>
                        <span class="option-text">✗ 错误</span>
                        <span class="option-indicator"></span>
                    </label>
                </div>
            </div>
        `;
    }

    /**
     * Set up question-specific interactions
     * @param {Object} question - Question object
     */
    setupQuestionInteractions(question) {
        // Set up radio button change listeners
        const radioButtons = this.quizContainer.querySelectorAll('input[name="quiz-answer"]');
        radioButtons.forEach(radio => {
            radio.addEventListener('change', (e) => {
                this.selectAnswer(e.target.value);
            });
        });
    }

    /**
     * Select an answer for the current question
     * @param {*} answer - Selected answer
     */
    selectAnswer(answer) {
        this.currentQuiz.answers[this.currentQuiz.currentQuestion] = answer;

        // Enable next button
        const nextButton = this.quizContainer.querySelector('.quiz-btn.primary');
        if (nextButton) {
            nextButton.disabled = false;
        }
    }

    /**
     * Check if an answer is selected for current question
     * @returns {boolean} Whether answer is selected
     */
    isAnswerSelected() {
        return this.currentQuiz.answers[this.currentQuiz.currentQuestion] !== null;
    }

    /**
     * Move to next question or complete quiz
     */
    nextQuestion() {
        if (this.currentQuiz.currentQuestion < this.currentQuiz.questions.length - 1) {
            this.currentQuiz.currentQuestion++;
            this.displayQuiz();
        } else {
            this.completeQuiz();
        }
    }

    /**
     * Move to previous question
     */
    previousQuestion() {
        if (this.currentQuiz.currentQuestion > 0) {
            this.currentQuiz.currentQuestion--;
            this.displayQuiz();
        }
    }

    /**
     * Complete the quiz and show results
     */
    completeQuiz() {
        this.calculateScore();
        this.displayResults();
        this.saveQuizResult();

        // Dispatch completion event
        this.dispatchQuizEvent('quiz:completed', {
            eventId: this.currentQuiz.eventId,
            score: this.currentQuiz.score,
            totalQuestions: this.currentQuiz.questions.length,
            percentage: Math.round((this.currentQuiz.score / this.currentQuiz.questions.length) * 100)
        });
    }

    /**
     * Calculate quiz score
     */
    calculateScore() {
        this.currentQuiz.score = this.currentQuiz.questions.reduce((score, question, index) => {
            const userAnswer = this.currentQuiz.answers[index];
            const correctAnswer = question.shuffledOptions ?
                question.shuffledOptions.correctIndex :
                question.answer;

            return score + (userAnswer == correctAnswer ? question.points : 0);
        }, 0);

        this.currentQuiz.endTime = Date.now();
        this.currentQuiz.duration = this.currentQuiz.endTime - this.currentQuiz.startTime;
    }

    /**
     * Display quiz results
     */
    displayResults() {
        const percentage = Math.round((this.currentQuiz.score / this.currentQuiz.questions.length) * 100);
        const duration = Math.round(this.currentQuiz.duration / 1000);

        let performanceMessage = '';
        if (percentage >= 90) {
            performanceMessage = '🎉 优秀！你对这个历史事件有深入的理解！';
        } else if (percentage >= 70) {
            performanceMessage = '👍 很好！继续学习以加深理解。';
        } else if (percentage >= 50) {
            performanceMessage = '📚 不错的开始，建议重新阅读相关内容。';
        } else {
            performanceMessage = '💪 继续努力！多花时间学习这个主题。';
        }

        this.quizContainer.innerHTML = `
            <div class="quiz-results">
                <div class="results-header">
                    <h2>🎯 测验完成！</h2>
                    <div class="score-display">
                        <div class="score-circle">
                            <span class="score-number">${percentage}%</span>
                        </div>
                        <p class="score-details">
                            ${this.currentQuiz.score} / ${this.currentQuiz.questions.length} 题正确
                        </p>
                    </div>
                </div>

                <div class="performance-message">
                    <p>${performanceMessage}</p>
                </div>

                <div class="quiz-stats">
                    <div class="stat-item">
                        <span class="stat-label">用时</span>
                        <span class="stat-value">${duration} 秒</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">尝试次数</span>
                        <span class="stat-value">${this.currentQuiz.attempts}</span>
                    </div>
                </div>

                <div class="quiz-actions">
                    <button class="quiz-btn secondary" onclick="quizEngine.retakeQuiz()">
                        🔄 重新测验
                    </button>
                    <button class="quiz-btn primary" onclick="quizEngine.closeQuiz()">
                        ✅ 完成
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * Save quiz result to history
     */
    saveQuizResult() {
        const result = {
            eventId: this.currentQuiz.eventId,
            score: this.currentQuiz.score,
            totalQuestions: this.currentQuiz.questions.length,
            percentage: Math.round((this.currentQuiz.score / this.currentQuiz.questions.length) * 100),
            duration: this.currentQuiz.duration,
            attempts: this.currentQuiz.attempts,
            timestamp: Date.now()
        };

        this.quizHistory.push(result);
        this.saveQuizHistory();
    }

    /**
     * Load quiz history from storage
     */
    loadQuizHistory() {
        try {
            const stored = localStorage.getItem('quizHistory');
            this.quizHistory = stored ? JSON.parse(stored) : [];
        } catch (error) {
            console.error('Failed to load quiz history:', error);
            this.quizHistory = [];
        }
    }

    /**
     * Save quiz history to storage
     */
    saveQuizHistory() {
        try {
            localStorage.setItem('quizHistory', JSON.stringify(this.quizHistory));
        } catch (error) {
            console.error('Failed to save quiz history:', error);
        }
    }

    /**
     * Get attempt count for an event
     * @param {number} eventId - Event ID
     * @returns {number} Attempt count
     */
    getAttemptCount(eventId) {
        return this.quizHistory.filter(result => result.eventId === eventId).length;
    }

    /**
     * Find event by ID
     * @param {number} eventId - Event ID
     * @returns {Object|null} Event object
     */
    findEventById(eventId) {
        if (typeof window.allHistoricalEvents !== 'undefined') {
            return window.allHistoricalEvents.find(event => event.id === eventId);
        }
        return null;
    }

    /**
     * Retake current quiz
     */
    retakeQuiz() {
        if (this.currentQuiz) {
            this.startQuiz(this.currentQuiz.eventId);
        }
    }

    /**
     * Close quiz interface
     */
    closeQuiz() {
        if (this.quizContainer) {
            this.quizContainer.style.display = 'none';
        }
        this.currentQuiz = null;
    }

    /**
     * Dispatch quiz event
     * @param {string} eventType - Event type
     * @param {Object} detail - Event detail
     */
    dispatchQuizEvent(eventType, detail = {}) {
        const event = new CustomEvent(eventType, { detail });
        window.dispatchEvent(event);
    }

    /**
     * Get quiz statistics
     * @returns {Object} Quiz statistics
     */
    getQuizStats() {
        return {
            totalQuizzes: this.quizHistory.length,
            averageScore: this.calculateAverageScore(),
            bestScore: this.getBestScore(),
            totalTime: this.getTotalTime()
        };
    }

    /**
     * Calculate average score
     * @returns {number} Average score percentage
     */
    calculateAverageScore() {
        if (this.quizHistory.length === 0) return 0;

        const totalPercentage = this.quizHistory.reduce((sum, result) => sum + result.percentage, 0);
        return Math.round(totalPercentage / this.quizHistory.length);
    }

    /**
     * Get best score
     * @returns {number} Best score percentage
     */
    getBestScore() {
        if (this.quizHistory.length === 0) return 0;

        return Math.max(...this.quizHistory.map(result => result.percentage));
    }

    /**
     * Get total time spent on quizzes
     * @returns {number} Total time in milliseconds
     */
    getTotalTime() {
        return this.quizHistory.reduce((sum, result) => sum + (result.duration || 0), 0);
    }

    /**
     * Destroy quiz engine
     */
    destroy() {
        this.currentQuiz = null;
        this.quizHistory = [];
        this.isInitialized = false;
        console.log('Quiz engine destroyed');
    }
}

// Create singleton instance
const quizEngine = new QuizEngine();

// Export for module use
export default quizEngine;

// Legacy function for backward compatibility
export function generateQuiz(event, containerId) {
    console.warn('generateQuiz is deprecated. Use quizEngine.startQuiz() instead.');
    if (event && event.id) {
        quizEngine.startQuiz(event.id);
    }
}