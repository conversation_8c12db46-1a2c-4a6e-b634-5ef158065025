/**
 * Theme Manager Module
 * Handles theme switching, persistence, and system preference detection
 */

class ThemeManager {
    constructor() {
        this.currentTheme = 'light';
        this.themeToggle = null;
        this.themeIcon = null;
        this.isInitialized = false;
        
        // Theme configuration
        this.themes = {
            light: {
                name: '浅色主题',
                icon: '🌙',
                class: 'light-theme'
            },
            dark: {
                name: '深色主题',
                icon: '☀️',
                class: 'dark-theme'
            }
        };
        
        this.init();
    }
    
    /**
     * Initialize theme manager
     */
    init() {
        if (this.isInitialized) return;
        
        // Get theme toggle elements
        this.themeToggle = document.getElementById('theme-toggle');
        this.themeIcon = this.themeToggle?.querySelector('.theme-icon');
        
        // Detect initial theme
        this.detectInitialTheme();
        
        // Set up event listeners
        this.setupEventListeners();
        
        // Apply initial theme
        this.applyTheme(this.currentTheme);
        
        this.isInitialized = true;
        console.log('Theme manager initialized with theme:', this.currentTheme);
    }
    
    /**
     * Detect initial theme based on user preference or system setting
     */
    detectInitialTheme() {
        // Check localStorage first
        const savedTheme = localStorage.getItem('preferred-theme');
        if (savedTheme && this.themes[savedTheme]) {
            this.currentTheme = savedTheme;
            return;
        }
        
        // Check system preference
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            this.currentTheme = 'dark';
        } else {
            this.currentTheme = 'light';
        }
    }
    
    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Theme toggle button
        if (this.themeToggle) {
            this.themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
            
            // Keyboard support
            this.themeToggle.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.toggleTheme();
                }
            });
        }
        
        // Listen for system theme changes
        if (window.matchMedia) {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            mediaQuery.addEventListener('change', (e) => {
                // Only auto-switch if user hasn't manually set a preference
                if (!localStorage.getItem('preferred-theme')) {
                    const newTheme = e.matches ? 'dark' : 'light';
                    this.setTheme(newTheme);
                }
            });
        }
    }
    
    /**
     * Toggle between light and dark themes
     */
    toggleTheme() {
        const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.setTheme(newTheme);
    }
    
    /**
     * Set specific theme
     * @param {string} themeName - Theme name ('light' or 'dark')
     */
    setTheme(themeName) {
        if (!this.themes[themeName]) {
            console.warn(`Unknown theme: ${themeName}`);
            return;
        }
        
        const oldTheme = this.currentTheme;
        this.currentTheme = themeName;
        
        // Apply theme
        this.applyTheme(themeName);
        
        // Save preference
        this.saveThemePreference(themeName);
        
        // Update UI
        this.updateThemeToggleUI();
        
        // Dispatch theme change event
        this.dispatchThemeChangeEvent(oldTheme, themeName);
        
        console.log(`Theme changed from ${oldTheme} to ${themeName}`);
    }
    
    /**
     * Apply theme to document
     * @param {string} themeName - Theme name
     */
    applyTheme(themeName) {
        const theme = this.themes[themeName];
        if (!theme) return;
        
        // Remove all theme classes
        Object.values(this.themes).forEach(t => {
            document.body.classList.remove(t.class);
        });
        
        // Add current theme class
        document.body.classList.add(theme.class);
        
        // Update CSS custom properties if needed
        this.updateCSSProperties(themeName);
        
        // Update meta theme-color for mobile browsers
        this.updateMetaThemeColor(themeName);
    }
    
    /**
     * Update CSS custom properties for theme
     * @param {string} themeName - Theme name
     */
    updateCSSProperties(themeName) {
        const root = document.documentElement;
        
        const themeColors = {
            light: {
                '--primary-color': '#2c3e50',
                '--secondary-color': '#3498db',
                '--background-color': '#ffffff',
                '--text-color': '#2c3e50',
                '--border-color': '#e1e8ed',
                '--shadow-color': 'rgba(0, 0, 0, 0.1)'
            },
            dark: {
                '--primary-color': '#ecf0f1',
                '--secondary-color': '#3498db',
                '--background-color': '#2c3e50',
                '--text-color': '#ecf0f1',
                '--border-color': '#34495e',
                '--shadow-color': 'rgba(0, 0, 0, 0.3)'
            }
        };
        
        const colors = themeColors[themeName];
        if (colors) {
            Object.entries(colors).forEach(([property, value]) => {
                root.style.setProperty(property, value);
            });
        }
    }
    
    /**
     * Update meta theme-color for mobile browsers
     * @param {string} themeName - Theme name
     */
    updateMetaThemeColor(themeName) {
        const metaThemeColor = document.querySelector('meta[name="theme-color"]');
        if (metaThemeColor) {
            const colors = {
                light: '#2c3e50',
                dark: '#2c3e50'
            };
            metaThemeColor.setAttribute('content', colors[themeName] || colors.light);
        }
    }
    
    /**
     * Update theme toggle button UI
     */
    updateThemeToggleUI() {
        if (!this.themeToggle || !this.themeIcon) return;
        
        const theme = this.themes[this.currentTheme];
        
        // Update icon
        this.themeIcon.textContent = theme.icon;
        
        // Update aria-label
        const nextTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        const nextThemeName = this.themes[nextTheme].name;
        this.themeToggle.setAttribute('aria-label', `切换到${nextThemeName}`);
        
        // Update title
        this.themeToggle.title = `切换到${nextThemeName}`;
    }
    
    /**
     * Save theme preference to localStorage
     * @param {string} themeName - Theme name
     */
    saveThemePreference(themeName) {
        try {
            localStorage.setItem('preferred-theme', themeName);
        } catch (error) {
            console.warn('Failed to save theme preference:', error);
        }
    }
    
    /**
     * Dispatch theme change event
     * @param {string} oldTheme - Previous theme
     * @param {string} newTheme - New theme
     */
    dispatchThemeChangeEvent(oldTheme, newTheme) {
        const event = new CustomEvent('themechange', {
            detail: {
                oldTheme,
                newTheme,
                timestamp: Date.now()
            }
        });
        
        window.dispatchEvent(event);
    }
    
    /**
     * Get current theme
     * @returns {string} Current theme name
     */
    getCurrentTheme() {
        return this.currentTheme;
    }
    
    /**
     * Get theme configuration
     * @param {string} themeName - Theme name
     * @returns {Object|null} Theme configuration
     */
    getThemeConfig(themeName) {
        return this.themes[themeName] || null;
    }
    
    /**
     * Check if theme is available
     * @param {string} themeName - Theme name
     * @returns {boolean} Whether theme is available
     */
    isThemeAvailable(themeName) {
        return Boolean(this.themes[themeName]);
    }
    
    /**
     * Get all available themes
     * @returns {Array} Array of theme names
     */
    getAvailableThemes() {
        return Object.keys(this.themes);
    }
}

// Create singleton instance
const themeManager = new ThemeManager();

// Export for module use
export default themeManager;

// Also attach to window for global access
window.ThemeManager = themeManager;
