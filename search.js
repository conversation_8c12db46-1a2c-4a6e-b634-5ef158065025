// 初始化搜索功能
function setupSearchFunctionality() {
    try {
        const searchInput = document.getElementById('search-input');
        const searchResults = document.getElementById('search-results');
        
        if (!searchInput || !searchResults) return;
        
        // 添加输入事件监听器
        searchInput.addEventListener('input', () => {
            const query = searchInput.value.trim().toLowerCase();
            
            if (query.length < 2) {
                searchResults.innerHTML = '';
                searchResults.style.display = 'none';
                return;
            }
            
            // 使用防抖动避免频繁搜索
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }
            
            searchTimeout = setTimeout(() => {
                // 执行搜索
                const results = performSearch(query);
                
                // 显示结果
                displaySearchResults(results, searchResults, query);
                searchResults.style.display = results.length > 0 ? 'block' : 'none';
            }, 300); // 300ms防抖延迟
        });
        
        // 点击外部关闭搜索结果
        document.addEventListener('click', (e) => {
            if (!searchInput.contains(e.target)) {
                searchResults.style.display = 'none';
            }
        });
        
        // 添加键盘导航支持
        searchResults.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                searchResults.style.display = 'none';
                searchInput.focus();
            }
        });
    } catch (error) {
        console.error('Search functionality setup failed:', error);
        throw error;
    }
}

// 执行搜索
function performSearch(query) {
    if (!allHistoricalEvents || allHistoricalEvents.length === 0) {
        return [];
    }
    
    // 统一处理查询参数
    query = query.trim().toLowerCase();
    if (query.length < 2) return [];
    
    // 创建或获取搜索索引
    const index = buildSearchIndex();
    
    // 使用 Fuse.js 进行模糊搜索
    const fuse = new Fuse(index, { keys: ['title', 'period', 'region', 'content'] });
    const results = fuse.search(query);
    
    // 从索引结果中提取原始事件数据
    return results.map(result => {
        const originalEvent = allHistoricalEvents.find(e => e.id === result.item.id);
        return originalEvent || result.item;
    });
}

// 构建搜索索引
let searchIndex = [];
let lastBuildTime = 0;
const INDEX_TTL = 30000; // 索引有效期为30秒

function buildSearchIndex() {
    if (searchIndex && Date.now() - lastBuildTime < INDEX_TTL) {
        return searchIndex; // 如果索引有效，直接返回
    }
    
    searchIndex = [];
    if (!allHistoricalEvents || allHistoricalEvents.length === 0) {
        return searchIndex;
    }
    
    allHistoricalEvents.forEach(event => {
        const searchableContent = [];
        
        // 添加字符串属性
        const stringProps = ['title', 'period', 'description', 'region'];
        stringProps.forEach(prop => {
            if (event[prop]) {
                searchableContent.push(event[prop]);
            }
        });
        
        // 添加多维分析内容
        const analysisDimensions = ['philosophy', 'psychology', 'sociology', 'economics', 'aiInsights'];
        analysisDimensions.forEach(dim => {
            if (event[dim]) {
                if (event[dim].content) {
                    searchableContent.push(event[dim].content);
                }
                if (Array.isArray(event[dim].questions)) {
                    event[dim].questions.forEach(question => {
                        searchableContent.push(question);
                    });
                }
                if (event[dim].glossaryTerms && typeof event[dim].glossaryTerms === 'object') {
                    Object.entries(event[dim].glossaryTerms).forEach(([term, definition]) => {
                        searchableContent.push(term);
                        searchableContent.push(definition);
                    });
                }
            }
        });
        
        // 添加测验数据
        if (event.quiz && event.quiz.questions) {
            event.quiz.questions.forEach(q => {
                searchableContent.push(q.question);
                q.options.forEach(opt => searchableContent.push(opt));
            });
        }
        
        // 创建预处理的搜索文本
        const fullText = searchableContent.join(' ').toLowerCase();
        
        searchIndex.push({
            id: event.id,
            title: event.title,
            period: event.period,
            region: event.region,
            content: fullText
        });
    });
    
    lastBuildTime = Date.now();
    return searchIndex;
}

// 显示搜索结果
function displaySearchResults(results, container, query) {
    if (!container) return;
    
    if (results.length === 0) {
        container.innerHTML = '<div class="search-result-item">未找到匹配的结果</div>';
        return;
    }
    
    // 构建结果HTML
    const resultsHTML = results.slice(0, 10).map(event => {
        return `
            <div class="search-result-item" onclick="renderEventDetail(${event.id})">
                <h4>${event.title}</h4>
                <p class="result-period">时期: ${event.period}</p>
                <p class="result-snippet">
                    ${highlightMatch(event.description, query, 200)}
                </p>
                <div class="result-tags">
                    ${Object.keys(event)
                        .filter(key => ['philosophy', 'psychology', 'sociology', 'economics'].includes(key) && event[key])
                        .map(key => `<span class="tag tag-${key}">${{
                            'philosophy': '哲学',
                            'psychology': '心理学',
                            'sociology': '社会学',
                            'economics': '经济学'
                        }[key]}</span>`)
                        .join('')}
                </div>
            </div>
        `;
    }).join('');
    
    container.innerHTML = resultsHTML;
    
    // 添加键盘导航支持
    let selectedIndex = -1;
    const resultItems = container.querySelectorAll('.search-result-item');
    
    container.addEventListener('keydown', (e) => {
        if (e.key === 'ArrowDown') {
            e.preventDefault();
            selectedIndex = Math.min(selectedIndex + 1, resultItems.length - 1);
            highlightItem(resultItems, selectedIndex);
        } else if (e.key === 'ArrowUp') {
            e.preventDefault();
            selectedIndex = Math.max(selectedIndex - 1, -1);
            highlightItem(resultItems, selectedIndex);
        } else if (e.key === 'Enter' && selectedIndex >= 0) {
            resultItems[selectedIndex].click();
        }
    });
}

// 高亮选中的搜索结果项
function highlightItem(items, index) {
    items.forEach((item, i) => {
        item.classList.toggle('selected', i === index);
    });
}
