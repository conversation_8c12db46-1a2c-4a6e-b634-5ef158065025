/**
 * Comprehensive Error Handling Module
 * Provides centralized error management, logging, and user feedback
 */

class ErrorHandler {
    constructor() {
        this.errorQueue = [];
        this.maxQueueSize = 50;
        this.isInitialized = false;
        this.errorBoundary = null;
        this.retryButton = null;
        
        this.init();
    }
    
    /**
     * Initialize error handling system
     */
    init() {
        if (this.isInitialized) return;
        
        // Get error boundary elements
        this.errorBoundary = document.getElementById('error-boundary');
        this.retryButton = document.getElementById('retry-button');
        
        // Set up global error handlers
        this.setupGlobalHandlers();
        
        // Set up retry functionality
        if (this.retryButton) {
            this.retryButton.addEventListener('click', () => this.handleRetry());
        }
        
        this.isInitialized = true;
        console.log('Error handling system initialized');
    }
    
    /**
     * Set up global error event listeners
     */
    setupGlobalHandlers() {
        // JavaScript errors
        window.addEventListener('error', (event) => {
            this.handleError({
                type: 'javascript',
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error,
                stack: event.error?.stack
            });
        });
        
        // Unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            this.handleError({
                type: 'promise',
                message: event.reason?.message || 'Unhandled promise rejection',
                reason: event.reason,
                stack: event.reason?.stack
            });
        });
        
        // Resource loading errors
        window.addEventListener('error', (event) => {
            if (event.target !== window) {
                this.handleError({
                    type: 'resource',
                    message: `Failed to load resource: ${event.target.src || event.target.href}`,
                    element: event.target.tagName,
                    source: event.target.src || event.target.href
                });
            }
        }, true);
    }
    
    /**
     * Handle different types of errors
     * @param {Object} errorInfo - Error information object
     */
    handleError(errorInfo) {
        // Add timestamp and ID
        const error = {
            id: this.generateErrorId(),
            timestamp: new Date().toISOString(),
            ...errorInfo
        };
        
        // Add to queue
        this.addToQueue(error);
        
        // Log error
        this.logError(error);
        
        // Determine if error should be shown to user
        if (this.shouldShowToUser(error)) {
            this.showErrorToUser(error);
        }
        
        // Send to analytics (if available)
        this.sendToAnalytics(error);
    }
    
    /**
     * Add error to internal queue
     * @param {Object} error - Error object
     */
    addToQueue(error) {
        this.errorQueue.push(error);
        
        // Maintain queue size
        if (this.errorQueue.length > this.maxQueueSize) {
            this.errorQueue.shift();
        }
    }
    
    /**
     * Log error to console with appropriate level
     * @param {Object} error - Error object
     */
    logError(error) {
        const logMessage = `[${error.type.toUpperCase()}] ${error.message}`;
        
        switch (error.type) {
            case 'javascript':
            case 'promise':
                console.error(logMessage, error);
                break;
            case 'resource':
                console.warn(logMessage, error);
                break;
            case 'network':
                console.error(logMessage, error);
                break;
            default:
                console.log(logMessage, error);
        }
    }
    
    /**
     * Determine if error should be shown to user
     * @param {Object} error - Error object
     * @returns {boolean} Whether to show error
     */
    shouldShowToUser(error) {
        // Don't show resource loading errors for non-critical resources
        if (error.type === 'resource' && this.isNonCriticalResource(error.source)) {
            return false;
        }
        
        // Show critical errors
        return ['javascript', 'promise', 'network'].includes(error.type);
    }
    
    /**
     * Check if resource is non-critical
     * @param {string} source - Resource source
     * @returns {boolean} Whether resource is non-critical
     */
    isNonCriticalResource(source) {
        if (!source) return true;
        
        const nonCriticalPatterns = [
            /analytics/i,
            /tracking/i,
            /ads/i,
            /social/i
        ];
        
        return nonCriticalPatterns.some(pattern => pattern.test(source));
    }
    
    /**
     * Show error to user with appropriate UI
     * @param {Object} error - Error object
     */
    showErrorToUser(error) {
        if (!this.errorBoundary) return;
        
        const errorMessage = this.getUserFriendlyMessage(error);
        const messageElement = document.getElementById('error-message');
        
        if (messageElement) {
            messageElement.textContent = errorMessage;
        }
        
        this.errorBoundary.style.display = 'block';
        this.errorBoundary.setAttribute('aria-hidden', 'false');
        
        // Auto-hide after delay for non-critical errors
        if (!this.isCriticalError(error)) {
            setTimeout(() => {
                this.hideErrorBoundary();
            }, 5000);
        }
    }
    
    /**
     * Get user-friendly error message
     * @param {Object} error - Error object
     * @returns {string} User-friendly message
     */
    getUserFriendlyMessage(error) {
        const messages = {
            javascript: '页面功能出现问题，请刷新页面重试',
            promise: '数据加载失败，请检查网络连接',
            network: '网络连接出现问题，请检查您的网络设置',
            resource: '部分资源加载失败，可能影响页面显示',
            default: '出现未知错误，请刷新页面重试'
        };
        
        return messages[error.type] || messages.default;
    }
    
    /**
     * Check if error is critical
     * @param {Object} error - Error object
     * @returns {boolean} Whether error is critical
     */
    isCriticalError(error) {
        return ['javascript', 'promise'].includes(error.type);
    }
    
    /**
     * Hide error boundary
     */
    hideErrorBoundary() {
        if (this.errorBoundary) {
            this.errorBoundary.style.display = 'none';
            this.errorBoundary.setAttribute('aria-hidden', 'true');
        }
    }
    
    /**
     * Handle retry button click
     */
    handleRetry() {
        this.hideErrorBoundary();
        
        // Attempt to reload the page or reinitialize
        if (typeof window.App?.init === 'function') {
            window.App.init();
        } else {
            window.location.reload();
        }
    }
    
    /**
     * Generate unique error ID
     * @returns {string} Error ID
     */
    generateErrorId() {
        return `err_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    }
    
    /**
     * Send error to analytics service
     * @param {Object} error - Error object
     */
    sendToAnalytics(error) {
        // Only send in production and if analytics is available
        if (typeof process !== 'undefined' && process.env?.NODE_ENV !== 'production') {
            return;
        }
        
        if (typeof window.gtag !== 'function') {
            return;
        }
        
        try {
            window.gtag('event', 'exception', {
                description: error.message,
                fatal: this.isCriticalError(error)
            });
        } catch (e) {
            console.warn('Failed to send error to analytics:', e);
        }
    }
    
    /**
     * Get error statistics
     * @returns {Object} Error statistics
     */
    getErrorStats() {
        const stats = {
            total: this.errorQueue.length,
            byType: {},
            recent: this.errorQueue.slice(-10)
        };
        
        this.errorQueue.forEach(error => {
            stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
        });
        
        return stats;
    }
    
    /**
     * Clear error queue
     */
    clearErrors() {
        this.errorQueue = [];
        this.hideErrorBoundary();
    }
}

// Create singleton instance
const errorHandler = new ErrorHandler();

// Export for module use
export default errorHandler;

// Also attach to window for global access
window.ErrorHandler = errorHandler;
