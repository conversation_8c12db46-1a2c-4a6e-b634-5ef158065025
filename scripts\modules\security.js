/**
 * Security Module
 * Provides security utilities and input sanitization
 */

class SecurityManager {
    constructor() {
        this.isInitialized = false;
        this.cspViolations = [];
        this.maxViolations = 50;
        
        // Security configuration
        this.config = {
            maxInputLength: 10000,
            allowedTags: ['b', 'i', 'em', 'strong', 'mark'],
            blockedPatterns: [
                /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
                /javascript:/gi,
                /on\w+\s*=/gi,
                /data:text\/html/gi
            ]
        };
    }
    
    /**
     * Initialize security manager
     */
    init() {
        if (this.isInitialized) return;
        
        // Set up CSP violation reporting
        this.setupCSPReporting();
        
        // Set up security headers validation
        this.validateSecurityHeaders();
        
        // Set up input sanitization
        this.setupGlobalInputSanitization();
        
        this.isInitialized = true;
        console.log('Security manager initialized');
    }
    
    /**
     * Set up Content Security Policy violation reporting
     */
    setupCSPReporting() {
        document.addEventListener('securitypolicyviolation', (e) => {
            this.handleCSPViolation(e);
        });
    }
    
    /**
     * Handle CSP violations
     * @param {SecurityPolicyViolationEvent} event - CSP violation event
     */
    handleCSPViolation(event) {
        const violation = {
            timestamp: new Date().toISOString(),
            directive: event.violatedDirective,
            blockedURI: event.blockedURI,
            sourceFile: event.sourceFile,
            lineNumber: event.lineNumber,
            columnNumber: event.columnNumber
        };
        
        // Store violation
        this.cspViolations.push(violation);
        
        // Maintain violations list size
        if (this.cspViolations.length > this.maxViolations) {
            this.cspViolations.shift();
        }
        
        // Log violation
        console.warn('CSP Violation:', violation);
        
        // Report to error handler if available
        if (window.ErrorHandler) {
            window.ErrorHandler.handleError({
                type: 'security',
                message: `CSP violation: ${event.violatedDirective}`,
                details: violation
            });
        }
    }
    
    /**
     * Validate security headers
     */
    validateSecurityHeaders() {
        // Check if running over HTTPS in production
        if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
            console.warn('Application should be served over HTTPS in production');
        }
        
        // Check for security headers (if accessible)
        this.checkSecurityHeaders();
    }
    
    /**
     * Check for important security headers
     */
    checkSecurityHeaders() {
        // Note: Most security headers are not accessible via JavaScript
        // This is a basic check for what we can detect
        
        const warnings = [];
        
        // Check if we're in an iframe (clickjacking protection)
        if (window !== window.top) {
            warnings.push('Application is running in an iframe - ensure X-Frame-Options is set');
        }
        
        // Check for mixed content
        if (location.protocol === 'https:' && document.querySelectorAll('[src^="http:"]').length > 0) {
            warnings.push('Mixed content detected - some resources are loaded over HTTP');
        }
        
        if (warnings.length > 0) {
            console.warn('Security warnings:', warnings);
        }
    }
    
    /**
     * Set up global input sanitization
     */
    setupGlobalInputSanitization() {
        // Monitor form submissions
        document.addEventListener('submit', (e) => {
            this.sanitizeFormData(e.target);
        });
        
        // Monitor input changes for real-time validation
        document.addEventListener('input', (e) => {
            if (e.target.matches('input, textarea')) {
                this.validateInput(e.target);
            }
        });
    }
    
    /**
     * Sanitize form data
     * @param {HTMLFormElement} form - Form element
     */
    sanitizeFormData(form) {
        const inputs = form.querySelectorAll('input, textarea, select');
        
        inputs.forEach(input => {
            if (input.type !== 'file' && input.value) {
                input.value = this.sanitizeInput(input.value, input.dataset.sanitize || 'text');
            }
        });
    }
    
    /**
     * Validate input element
     * @param {HTMLInputElement} input - Input element
     */
    validateInput(input) {
        const value = input.value;
        const maxLength = parseInt(input.getAttribute('maxlength')) || this.config.maxInputLength;
        
        // Check length
        if (value.length > maxLength) {
            input.setCustomValidity(`输入内容过长，最多允许 ${maxLength} 个字符`);
            return false;
        }
        
        // Check for malicious patterns
        if (this.containsMaliciousContent(value)) {
            input.setCustomValidity('输入内容包含不允许的字符或代码');
            return false;
        }
        
        // Clear custom validity if all checks pass
        input.setCustomValidity('');
        return true;
    }
    
    /**
     * Sanitize input string
     * @param {string} input - Input string
     * @param {string} type - Sanitization type
     * @returns {string} Sanitized string
     */
    sanitizeInput(input, type = 'text') {
        if (typeof input !== 'string') {
            return '';
        }
        
        switch (type) {
            case 'html':
                return this.sanitizeHTML(input);
            case 'url':
                return this.sanitizeURL(input);
            case 'search':
                return this.sanitizeSearchQuery(input);
            case 'text':
            default:
                return this.sanitizeText(input);
        }
    }
    
    /**
     * Sanitize HTML content
     * @param {string} html - HTML string
     * @returns {string} Sanitized HTML
     */
    sanitizeHTML(html) {
        // Create a temporary element
        const temp = document.createElement('div');
        temp.innerHTML = html;
        
        // Remove script tags and event handlers
        this.removeScripts(temp);
        this.removeEventHandlers(temp);
        
        // Only allow specific tags
        this.filterAllowedTags(temp);
        
        return temp.innerHTML;
    }
    
    /**
     * Remove script tags and their content
     * @param {HTMLElement} element - Element to clean
     */
    removeScripts(element) {
        const scripts = element.querySelectorAll('script');
        scripts.forEach(script => script.remove());
    }
    
    /**
     * Remove event handlers from elements
     * @param {HTMLElement} element - Element to clean
     */
    removeEventHandlers(element) {
        const allElements = element.querySelectorAll('*');
        allElements.forEach(el => {
            // Remove all on* attributes
            Array.from(el.attributes).forEach(attr => {
                if (attr.name.startsWith('on')) {
                    el.removeAttribute(attr.name);
                }
            });
            
            // Remove javascript: URLs
            ['href', 'src', 'action'].forEach(attr => {
                if (el.getAttribute(attr) && el.getAttribute(attr).toLowerCase().startsWith('javascript:')) {
                    el.removeAttribute(attr);
                }
            });
        });
    }
    
    /**
     * Filter to only allowed HTML tags
     * @param {HTMLElement} element - Element to filter
     */
    filterAllowedTags(element) {
        const allElements = Array.from(element.querySelectorAll('*'));
        
        allElements.forEach(el => {
            if (!this.config.allowedTags.includes(el.tagName.toLowerCase())) {
                // Replace with text content
                const textNode = document.createTextNode(el.textContent);
                el.parentNode.replaceChild(textNode, el);
            }
        });
    }
    
    /**
     * Sanitize URL
     * @param {string} url - URL string
     * @returns {string} Sanitized URL
     */
    sanitizeURL(url) {
        try {
            const urlObj = new URL(url);
            
            // Only allow http and https protocols
            if (!['http:', 'https:'].includes(urlObj.protocol)) {
                return '';
            }
            
            return urlObj.toString();
        } catch (error) {
            return '';
        }
    }
    
    /**
     * Sanitize search query
     * @param {string} query - Search query
     * @returns {string} Sanitized query
     */
    sanitizeSearchQuery(query) {
        return query
            .trim()
            .replace(/[<>]/g, '')
            .replace(/['"]/g, '')
            .substring(0, 100);
    }
    
    /**
     * Sanitize text content
     * @param {string} text - Text content
     * @returns {string} Sanitized text
     */
    sanitizeText(text) {
        return text
            .trim()
            .replace(/[<>]/g, '')
            .replace(/\s+/g, ' ');
    }
    
    /**
     * Check if content contains malicious patterns
     * @param {string} content - Content to check
     * @returns {boolean} Whether content is malicious
     */
    containsMaliciousContent(content) {
        return this.config.blockedPatterns.some(pattern => pattern.test(content));
    }
    
    /**
     * Escape HTML entities
     * @param {string} text - Text to escape
     * @returns {string} Escaped text
     */
    escapeHTML(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    /**
     * Generate secure random string
     * @param {number} length - String length
     * @returns {string} Random string
     */
    generateSecureRandom(length = 16) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        
        if (window.crypto && window.crypto.getRandomValues) {
            const array = new Uint8Array(length);
            window.crypto.getRandomValues(array);
            
            for (let i = 0; i < length; i++) {
                result += chars[array[i] % chars.length];
            }
        } else {
            // Fallback for older browsers
            for (let i = 0; i < length; i++) {
                result += chars[Math.floor(Math.random() * chars.length)];
            }
        }
        
        return result;
    }
    
    /**
     * Validate and sanitize localStorage data
     * @param {string} key - Storage key
     * @param {*} data - Data to store
     * @returns {boolean} Whether data was stored successfully
     */
    secureLocalStorage(key, data) {
        try {
            // Validate key
            if (typeof key !== 'string' || key.length === 0) {
                throw new Error('Invalid storage key');
            }
            
            // Sanitize data
            const sanitizedData = this.sanitizeStorageData(data);
            
            // Store data
            localStorage.setItem(key, JSON.stringify(sanitizedData));
            return true;
            
        } catch (error) {
            console.error('Secure storage failed:', error);
            return false;
        }
    }
    
    /**
     * Sanitize data for storage
     * @param {*} data - Data to sanitize
     * @returns {*} Sanitized data
     */
    sanitizeStorageData(data) {
        if (typeof data === 'string') {
            return this.sanitizeText(data);
        }
        
        if (Array.isArray(data)) {
            return data.map(item => this.sanitizeStorageData(item));
        }
        
        if (typeof data === 'object' && data !== null) {
            const sanitized = {};
            Object.keys(data).forEach(key => {
                const sanitizedKey = this.sanitizeText(key);
                sanitized[sanitizedKey] = this.sanitizeStorageData(data[key]);
            });
            return sanitized;
        }
        
        return data;
    }
    
    /**
     * Get CSP violations
     * @returns {Array} Array of CSP violations
     */
    getCSPViolations() {
        return [...this.cspViolations];
    }
    
    /**
     * Clear CSP violations
     */
    clearCSPViolations() {
        this.cspViolations = [];
    }
    
    /**
     * Get security report
     * @returns {Object} Security report
     */
    getSecurityReport() {
        return {
            cspViolations: this.cspViolations.length,
            isHTTPS: location.protocol === 'https:',
            isInIframe: window !== window.top,
            timestamp: new Date().toISOString()
        };
    }
    
    /**
     * Destroy security manager
     */
    destroy() {
        this.cspViolations = [];
        this.isInitialized = false;
        console.log('Security manager destroyed');
    }
}

// Create singleton instance
const securityManager = new SecurityManager();

// Export for module use
export default securityManager;
