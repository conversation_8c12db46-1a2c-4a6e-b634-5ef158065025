/**
 * Event Rendering Module
 * Handles rendering and display of historical events
 */

class EventRenderer {
    constructor() {
        this.eventList = null;
        this.eventDetailContainer = null;
        this.isInitialized = false;
        this.currentEvents = [];
    }

    /**
     * Initialize the event renderer
     */
    init() {
        if (this.isInitialized) return;

        // Get DOM elements
        this.eventList = document.querySelector('.event-list');
        this.eventDetailContainer = document.getElementById('event-detail');

        if (!this.eventList) {
            console.warn('Event list container not found');
            return;
        }

        // Set up event listeners
        this.setupEventListeners();

        // Load initial events
        this.loadEvents();

        this.isInitialized = true;
        console.log('Event renderer initialized');
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Listen for search result selections
        window.addEventListener('search:result-selected', (e) => {
            this.renderEventDetail(e.detail.eventId);
        });

        // Listen for timeline selections
        window.addEventListener('timeline:event-selected', (e) => {
            this.renderEventDetail(e.detail.eventId);
        });
    }

    /**
     * Load and render events
     */
    loadEvents() {
        // Check if historical events data is available
        if (typeof window.allHistoricalEvents === 'undefined' || !window.allHistoricalEvents) {
            console.warn('Historical events data not available');
            this.showNoEventsMessage();
            return;
        }

        this.currentEvents = window.allHistoricalEvents;
        this.renderEventList(this.currentEvents);
    }

    /**
     * Render list of events
     * @param {Array} events - Array of events to render
     */
    renderEventList(events) {
        if (!this.eventList) return;

        if (!events || events.length === 0) {
            this.showNoEventsMessage();
            return;
        }

        // Clear existing content
        this.eventList.innerHTML = '';

        // Create event cards
        events.forEach(event => {
            const card = this.createEventCard(event);
            this.eventList.appendChild(card);
        });

        // Dispatch event
        this.dispatchEvent('events:rendered', { count: events.length });
    }

    /**
     * Create a single event card
     * @param {Object} event - Event data
     * @returns {HTMLElement} Event card element
     */
    createEventCard(event) {
        if (!event || !event.id) {
            console.warn('Invalid event data');
            return document.createElement('div');
        }

        const card = document.createElement('div');
        card.className = 'event-card';
        card.setAttribute('data-event-id', event.id);
        card.setAttribute('role', 'button');
        card.setAttribute('tabindex', '0');
        card.setAttribute('aria-label', `查看事件详情: ${event.title}`);

        // Build card content
        card.innerHTML = `
            <h3 class="event-title">${this.escapeHtml(event.title)}</h3>
            <p class="event-period">时期: ${this.escapeHtml(event.period)}</p>
            ${event.region ? `<p class="event-region">地区: ${this.escapeHtml(event.region)}</p>` : ''}
            <p class="event-description">${this.escapeHtml(this.truncateText(event.description, 150))}</p>
            <button class="view-details" data-id="${event.id}" aria-label="查看${event.title}的详细信息">
                查看详情
            </button>
        `;

        // Add click handlers
        this.addCardEventListeners(card, event);

        return card;
    }

    /**
     * Add event listeners to card
     * @param {HTMLElement} card - Card element
     * @param {Object} event - Event data
     */
    addCardEventListeners(card, event) {
        // Click handler
        const clickHandler = () => {
            this.renderEventDetail(event.id);
            this.scrollToDetail();
        };

        card.addEventListener('click', clickHandler);

        // Keyboard handler
        card.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                clickHandler();
            }
        });

        // Button specific handler
        const button = card.querySelector('.view-details');
        if (button) {
            button.addEventListener('click', (e) => {
                e.stopPropagation();
                clickHandler();
            });
        }
    }

    /**
     * Render detailed view of an event
     * @param {number} eventId - Event ID
     */
    renderEventDetail(eventId) {
        const event = this.findEventById(eventId);
        if (!event) {
            console.warn(`Event with ID ${eventId} not found`);
            return;
        }

        if (!this.eventDetailContainer) {
            console.warn('Event detail container not found');
            return;
        }

        // Build detail content
        const detailHTML = this.buildEventDetailHTML(event);
        this.eventDetailContainer.innerHTML = detailHTML;
        this.eventDetailContainer.style.display = 'block';

        // Set up detail interactions
        this.setupDetailInteractions(event);

        // Dispatch event
        this.dispatchEvent('event:detail-rendered', { eventId: event.id });
    }

    /**
     * Build HTML for event detail view
     * @param {Object} event - Event data
     * @returns {string} HTML string
     */
    buildEventDetailHTML(event) {
        return `
            <div class="event-detail-card">
                <header class="event-detail-header">
                    <h2>${this.escapeHtml(event.title)}</h2>
                    <div class="event-meta">
                        <span class="event-period">时期: ${this.escapeHtml(event.period)}</span>
                        ${event.region ? `<span class="event-region">地区: ${this.escapeHtml(event.region)}</span>` : ''}
                    </div>
                </header>

                <div class="event-content">
                    <section class="event-description">
                        <h3>事件描述</h3>
                        <p>${this.escapeHtml(event.description)}</p>
                    </section>

                    ${this.buildAnalysisHTML(event)}
                    ${this.buildMediaHTML(event)}
                </div>

                <footer class="event-detail-footer">
                    <button class="close-detail" aria-label="关闭详情">关闭</button>
                </footer>
            </div>
        `;
    }

    /**
     * Build analysis sections HTML
     * @param {Object} event - Event data
     * @returns {string} HTML string
     */
    buildAnalysisHTML(event) {
        const analysisDimensions = ['philosophy', 'psychology', 'sociology', 'economics'];
        const dimensionNames = {
            philosophy: '哲学视角',
            psychology: '心理学视角',
            sociology: '社会学视角',
            economics: '经济视角'
        };

        const analysisHTML = analysisDimensions
            .filter(dim => event[dim] && event[dim].content)
            .map(dim => `
                <section class="analysis-section ${dim}-analysis">
                    <h4>${dimensionNames[dim]}</h4>
                    <p>${this.escapeHtml(event[dim].content)}</p>
                    ${event[dim].questions ? this.buildQuestionsHTML(event[dim].questions) : ''}
                </section>
            `).join('');

        return analysisHTML ? `<div class="multi-analysis">${analysisHTML}</div>` : '';
    }

    /**
     * Build questions HTML
     * @param {Array} questions - Array of questions
     * @returns {string} HTML string
     */
    buildQuestionsHTML(questions) {
        if (!questions || questions.length === 0) return '';

        const questionsHTML = questions.map(q => `<li>${this.escapeHtml(q)}</li>`).join('');
        return `
            <div class="analysis-questions">
                <h5>思考问题</h5>
                <ul>${questionsHTML}</ul>
            </div>
        `;
    }

    /**
     * Build media HTML
     * @param {Object} _event - Event data (unused for now)
     * @returns {string} HTML string
     */
    buildMediaHTML(_event) {
        // Placeholder for media content
        return `
            <section class="event-media">
                <h4>相关媒体</h4>
                <div class="media-placeholder">
                    <p>媒体内容将在后续版本中添加</p>
                </div>
            </section>
        `;
    }

    /**
     * Set up interactions for detail view
     * @param {Object} _event - Event data (unused for now)
     */
    setupDetailInteractions(_event) {
        // Close button
        const closeButton = this.eventDetailContainer.querySelector('.close-detail');
        if (closeButton) {
            closeButton.addEventListener('click', () => {
                this.hideEventDetail();
            });
        }

        // Escape key to close
        const escapeHandler = (e) => {
            if (e.key === 'Escape') {
                this.hideEventDetail();
                document.removeEventListener('keydown', escapeHandler);
            }
        };
        document.addEventListener('keydown', escapeHandler);
    }

    /**
     * Hide event detail view
     */
    hideEventDetail() {
        if (this.eventDetailContainer) {
            this.eventDetailContainer.style.display = 'none';
        }
    }

    /**
     * Scroll to detail section
     */
    scrollToDetail() {
        if (this.eventDetailContainer) {
            this.eventDetailContainer.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    }

    /**
     * Update event display with filtered events
     * @param {Array} events - Filtered events
     */
    updateEventDisplay(events) {
        this.renderEventList(events);
    }

    /**
     * Show no events message
     */
    showNoEventsMessage() {
        if (this.eventList) {
            this.eventList.innerHTML = `
                <div class="no-events-message">
                    <h3>暂无历史事件</h3>
                    <p>请检查数据加载或尝试刷新页面</p>
                </div>
            `;
        }
    }

    /**
     * Find event by ID
     * @param {number} eventId - Event ID
     * @returns {Object|null} Event object or null
     */
    findEventById(eventId) {
        return this.currentEvents.find(event => event.id === eventId) || null;
    }

    /**
     * Truncate text to specified length
     * @param {string} text - Text to truncate
     * @param {number} maxLength - Maximum length
     * @returns {string} Truncated text
     */
    truncateText(text, maxLength) {
        if (!text || text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }

    /**
     * Escape HTML to prevent XSS
     * @param {string} text - Text to escape
     * @returns {string} Escaped text
     */
    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Dispatch custom event
     * @param {string} eventType - Event type
     * @param {Object} detail - Event detail
     */
    dispatchEvent(eventType, detail = {}) {
        const event = new CustomEvent(eventType, { detail });
        window.dispatchEvent(event);
    }

    /**
     * Get current events
     * @returns {Array} Current events array
     */
    getCurrentEvents() {
        return this.currentEvents;
    }

    /**
     * Destroy the renderer
     */
    destroy() {
        this.currentEvents = [];
        this.isInitialized = false;
        console.log('Event renderer destroyed');
    }
}

// Create singleton instance
const eventRenderer = new EventRenderer();

// Export for module use
export default eventRenderer;
